"""
广告系统相关的请求和响应模型
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class AdWatchRequest(BaseModel):
    """观看广告请求"""
    ad_type: str = Field(..., description="广告类型：task_reward, daily_bonus")
    task_id: Optional[str] = Field(None, description="任务ID（如果是任务奖励广告）")
    watch_duration: int = Field(default=30, description="观看时长(秒)")
    ad_provider: str = Field(default="default", description="广告提供商")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "ad_type": "task_reward",
                "task_id": "daily_login",
                "watch_duration": 30,
                "ad_provider": "unity_ads"
            }
        }
    }


class AdWatchResponse(BaseModel):
    """观看广告响应"""
    success: bool = Field(..., description="是否成功")
    ad_id: Optional[int] = Field(None, description="广告记录ID")
    reward_type: Optional[str] = Field(None, description="奖励类型")
    reward_amount: int = Field(default=0, description="奖励数量")
    reward_multiplier: int = Field(default=1, description="奖励倍数")
    final_amount: int = Field(default=0, description="最终奖励数量")
    message: str = Field(..., description="结果消息")


class AdStatusResponse(BaseModel):
    """广告状态响应"""
    ad_type: str = Field(..., description="广告类型")
    daily_limit: int = Field(..., description="每日限制")
    watched_today: int = Field(..., description="今日已观看")
    remaining_today: int = Field(..., description="今日剩余")
    last_watch_time: Optional[str] = Field(None, description="最后观看时间")
    can_watch: bool = Field(..., description="是否可以观看")


class UserAdStatusResponse(BaseModel):
    """用户广告状态响应"""
    user_id: str = Field(..., description="用户ID")
    date: str = Field(..., description="日期")
    ad_limits: List[AdStatusResponse] = Field(..., description="各类型广告状态")
    total_watched: int = Field(..., description="今日总观看次数")


class DoubleRewardRequest(BaseModel):
    """双倍奖励请求"""
    reward_type: str = Field(..., description="奖励类型：task, login")
    task_id: Optional[str] = Field(None, description="任务ID")
    original_amount: int = Field(..., description="原始奖励数量")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "reward_type": "task",
                "task_id": "daily_collect_10",
                "original_amount": 100
            }
        }
    }


class DoubleRewardResponse(BaseModel):
    """双倍奖励响应"""
    success: bool = Field(..., description="是否成功")
    original_amount: int = Field(..., description="原始奖励")
    bonus_amount: int = Field(..., description="额外奖励")
    total_amount: int = Field(..., description="总奖励")
    reward_type: str = Field(..., description="奖励类型")


class TaskProgressResponse(BaseModel):
    """任务进度响应"""
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_type: str = Field(..., description="任务类型")
    description: str = Field(..., description="任务描述")
    current_progress: int = Field(..., description="当前进度")
    target_progress: int = Field(..., description="目标进度")
    progress_rate: float = Field(..., description="进度百分比")
    is_completed: bool = Field(..., description="是否完成")
    is_rewarded: bool = Field(..., description="是否已领取奖励")
    reward_type: str = Field(..., description="奖励类型")
    reward_amount: int = Field(..., description="奖励数量")
    can_double_reward: bool = Field(..., description="是否可以观看广告双倍奖励")
    completed_at: Optional[str] = Field(None, description="完成时间")


class DailyTasksResponse(BaseModel):
    """每日任务响应"""
    date: str = Field(..., description="日期")
    tasks: List[TaskProgressResponse] = Field(..., description="任务列表")
    completed_count: int = Field(..., description="已完成任务数")
    total_count: int = Field(..., description="总任务数")
    completion_rate: float = Field(..., description="完成率")


class TaskRewardRequest(BaseModel):
    """任务奖励领取请求"""
    task_id: str = Field(..., description="任务ID")
    use_double_reward: bool = Field(default=False, description="是否使用双倍奖励")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "task_id": "daily_collect_10",
                "use_double_reward": True
            }
        }
    }


class TaskRewardResponse(BaseModel):
    """任务奖励响应"""
    success: bool = Field(..., description="是否成功")
    task_id: str = Field(..., description="任务ID")
    reward_type: str = Field(..., description="奖励类型")
    base_amount: int = Field(..., description="基础奖励")
    bonus_amount: int = Field(default=0, description="额外奖励")
    total_amount: int = Field(..., description="总奖励")
    is_double_reward: bool = Field(..., description="是否双倍奖励")


class LoginRewardResponse(BaseModel):
    """登录奖励响应"""
    date: str = Field(..., description="日期")
    consecutive_days: int = Field(..., description="连续登录天数")
    reward_type: str = Field(..., description="奖励类型")
    reward_amount: int = Field(..., description="奖励数量")
    is_claimed: bool = Field(..., description="是否已领取")
    can_claim: bool = Field(..., description="是否可以领取")
    can_double: bool = Field(..., description="是否可以双倍领取")
    next_reward_preview: Optional[Dict[str, Any]] = Field(None, description="明日奖励预览")


class LoginRewardClaimRequest(BaseModel):
    """登录奖励领取请求"""
    use_double_reward: bool = Field(default=False, description="是否使用双倍奖励")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "use_double_reward": True
            }
        }
    }


class LoginRewardClaimResponse(BaseModel):
    """登录奖励领取响应"""
    success: bool = Field(..., description="是否成功")
    consecutive_days: int = Field(..., description="连续登录天数")
    reward_type: str = Field(..., description="奖励类型")
    base_amount: int = Field(..., description="基础奖励")
    bonus_amount: int = Field(default=0, description="额外奖励")
    total_amount: int = Field(..., description="总奖励")
    is_double_reward: bool = Field(..., description="是否双倍奖励")
    new_balance: Dict[str, int] = Field(..., description="新的资源余额")
