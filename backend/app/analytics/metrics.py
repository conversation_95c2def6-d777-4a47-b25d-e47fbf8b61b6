"""
数据分析和指标监控模块
负责收集、聚合和监控游戏核心指标
"""
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio
import logging
from prometheus_client import Counter, Gauge, Histogram, Summary
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
import time

from app.core.config import settings
from app.core.redis_client import redis_manager
from app.core.database import db_manager

logger = logging.getLogger(__name__)

# ===== Prometheus 指标定义 =====

# 计数器指标
user_registration_counter = Counter(
    f'{settings.METRICS_PREFIX}_user_registration_total',
    'Total number of user registrations'
)

user_login_counter = Counter(
    f'{settings.METRICS_PREFIX}_user_login_total',
    'Total number of user logins',
    ['login_type']  # guest, facebook, google, apple
)

session_started_counter = Counter(
    f'{settings.METRICS_PREFIX}_session_started_total',
    'Total number of game sessions started',
    ['city_id']
)

session_completed_counter = Counter(
    f'{settings.METRICS_PREFIX}_session_completed_total',
    'Total number of game sessions completed',
    ['city_id']
)

hotspot_collected_counter = Counter(
    f'{settings.METRICS_PREFIX}_hotspot_collected_total',
    'Total number of hotspots collected',
    ['hotspot_type']  # thief, garbage, treasure, boss_thief
)

boss_defeated_counter = Counter(
    f'{settings.METRICS_PREFIX}_boss_defeated_total',
    'Total number of bosses defeated'
)

artifact_found_counter = Counter(
    f'{settings.METRICS_PREFIX}_artifact_found_total',
    'Total number of artifacts found',
    ['rarity']  # legendary, epic, rare, common
)

ad_watched_counter = Counter(
    f'{settings.METRICS_PREFIX}_ad_watched_total',
    'Total number of ads watched',
    ['ad_type']  
)

# 测量指标
session_duration_histogram = Histogram(
    f'{settings.METRICS_PREFIX}_session_duration_seconds',
    'Game session duration in seconds',
    buckets=(30, 60, 120, 300, 600, 1200, 1800, 3600)
)

api_latency_histogram = Histogram(
    f'{settings.METRICS_PREFIX}_api_latency_seconds',
    'API request latency',
    ['endpoint', 'method']
)

# 实时指标
active_users_gauge = Gauge(
    f'{settings.METRICS_PREFIX}_active_users',
    'Current number of active users'
)

db_connection_pool_gauge = Gauge(
    f'{settings.METRICS_PREFIX}_db_connection_pool_usage',
    'Database connection pool usage',
    ['pool_type']  # size, checked_in, checked_out, overflow
)

redis_memory_gauge = Gauge(
    f'{settings.METRICS_PREFIX}_redis_memory_bytes',
    'Redis memory usage in bytes'
)

# 经济指标
economy_counter = Counter(
    f'{settings.METRICS_PREFIX}_economy_total',
    'Economy metrics',
    ['currency', 'action'] 
)


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self):
        self._running = False
        self._task = None
    
    async def start(self):
        """启动指标收集"""
        if self._running:
            return
        
        self._running = True
        self._task = asyncio.create_task(self._collect_loop())
        logger.info("Metrics collector started")
    
    async def stop(self):
        """停止指标收集"""
        self._running = False
        if self._task:
            await self._task
        logger.info("Metrics collector stopped")
    
    async def _collect_loop(self):
        """收集循环"""
        while self._running:
            try:
                # 收集实时指标
                await self._collect_realtime_metrics()
                
                # 每5秒收集一次
                await asyncio.sleep(5)
                
            except Exception as e:
                logger.error(f"Error in metrics collection: {e}")
                await asyncio.sleep(10)
    
    async def _collect_realtime_metrics(self):
        """收集实时指标"""
        # 活跃用户数
        active_users = await self._get_active_users_count()
        active_users_gauge.set(active_users)
        
        # 数据库连接池状态
        if db_manager.engine:
            pool_status = db_manager.get_pool_status()
            db_connection_pool_gauge.labels(pool_type='size').set(pool_status.get('size', 0))
            db_connection_pool_gauge.labels(pool_type='checked_in').set(pool_status.get('checked_in', 0))
            db_connection_pool_gauge.labels(pool_type='checked_out').set(pool_status.get('checked_out', 0))
            db_connection_pool_gauge.labels(pool_type='overflow').set(pool_status.get('overflow', 0))
        
        # Redis 内存使用（模拟）
        redis_memory_gauge.set(100 * 1024 * 1024)  # 100MB
    
    async def _get_active_users_count(self) -> int:
        """获取活跃用户数"""
        # 从 Redis 获取最近5分钟的活跃用户
        key = f"active_users:{int(time.time() // 300)}"
        count = await redis_manager.redis_client.scard(key)
        return count or 0


class AnalyticsService:
    """数据分析服务"""
    
    def __init__(self):
        self.collector = MetricsCollector()

    async def init(self):
        """初始化分析服务"""
        logger.info("Analytics service initialized")
    
    # ===== 用户行为追踪 =====
    
    async def track_user_registration(self, user_id: str):
        """追踪用户注册"""
        user_registration_counter.inc()
        await redis_manager.increment_counter("user_registration")
        await self._track_daily_metric("user_registration", user_id)
    
    async def track_user_login(self, user_id: str, login_type: str = "guest"):
        """追踪用户登录"""
        user_login_counter.labels(login_type=login_type).inc()
        await redis_manager.increment_counter(f"user_login:{login_type}")
        await self._track_daily_metric("dau", user_id)
        await self._update_active_users(user_id)
    
    async def track_session_start(self, session_id: str, user_id: str, city_id: str):
        """追踪游戏会话开始"""
        session_started_counter.labels(city_id=city_id).inc()
        await redis_manager.increment_counter(f"session_started:{city_id}")
        await redis_manager.set_json(
            f"session_metrics:{session_id}",
            {"start_time": time.time(), "user_id": user_id, "city_id": city_id},
            expire=3600
        )
    
    async def track_session_end(self, session_id: str, duration: int):
        """追踪游戏会话结束"""
        session_data = await redis_manager.get_json(f"session_metrics:{session_id}")
        if session_data:
            city_id = session_data.get("city_id", "unknown")
            session_completed_counter.labels(city_id=city_id).inc()
            session_duration_histogram.observe(duration)
            await redis_manager.increment_counter(f"session_completed:{city_id}")
            await redis_manager.delete(f"session_metrics:{session_id}")
    
    async def track_hotspot_collected(self, hotspot_type: str):
        """追踪热点收集"""
        hotspot_collected_counter.labels(hotspot_type=hotspot_type).inc()
        await redis_manager.increment_counter(f"hotspot_collected:{hotspot_type}")
    
    async def track_boss_defeated(self, boss_id: str):
        """追踪BOSS击败"""
        boss_defeated_counter.inc()
        await redis_manager.increment_counter("boss_defeated")
    
    async def track_artifact_found(self, artifact_id: str, rarity: str):
        """追踪文物发现"""
        artifact_found_counter.labels(rarity=rarity).inc()
        await redis_manager.increment_counter(f"artifact_found:{rarity}")
    
    async def track_economy(self, currency: str, action: str, amount: int):
        """追踪经济数据"""
        economy_counter.labels(currency=currency, action=action).inc(amount)
        await redis_manager.increment_counter(f"economy:{currency}:{action}", amount)
    
    async def track_ad_watched(self, ad_type: str, user_id: str):
        """追踪广告观看"""
        ad_watched_counter.labels(ad_type=ad_type).inc()
        await redis_manager.increment_counter(f"ad_watched:{ad_type}")
    
    async def track_cannon_upgrade(self, upgrade_type: str, new_level: int):
        """追踪大炮升级"""
        # 记录大炮升级次数
        await redis_manager.increment_counter(f"cannon_upgrade:{upgrade_type}")
        await redis_manager.increment_counter("cannon_upgrade_total")
        
        # 记录等级分布
        await redis_manager.increment_counter(f"cannon_level:{upgrade_type}:{new_level}")
        
        logger.info(f"Cannon upgrade tracked: {upgrade_type} to level {new_level}")
    
    # ===== 数据查询和聚合 =====
    
    async def get_realtime_metrics(self) -> Dict[str, Any]:
        """获取实时指标"""
        metrics = {
            "active_users": active_users_gauge._value.get(),
            "today": {
                "dau": await self._get_daily_metric_count("dau"),
                "sessions_started": await redis_manager.get("counter:session_started") or 0,
                "ads_watched": await redis_manager.get("counter:ad_watched") or 0,
            }
        }
        return metrics
    
    async def get_user_analytics(self, user_id: str) -> Dict[str, Any]:
        """获取用户分析数据"""
        # 从数据库获取用户统计
        async with db_manager.session() as session:
            # 这里需要实际的SQL查询
            pass
        
        return {
            "total_sessions": 0,
            "total_play_time": 0,
            "artifacts_collected": 0,
            "last_active": datetime.utcnow()
        }
    
    async def get_retention_rate(self, days: int = 7) -> Dict[str, float]:
        """获取留存率"""
        retention = {}
        for i in range(1, days + 1):
            # 计算第i天的留存率
            retention[f"day_{i}"] = 0.0  # 需要实际计算
        return retention
    
    # ===== 私有方法 =====
    
    async def _track_daily_metric(self, metric_name: str, user_id: str):
        """追踪每日指标"""
        count = await redis_manager.track_daily_metric(metric_name, user_id)
        
        # 检查告警阈值
        if metric_name == "dau" and count < 100:  # 示例：DAU低于100时告警
            logger.warning(f"Low DAU detected: {count}")
    
    async def _update_active_users(self, user_id: str):
        """更新活跃用户"""
        # 5分钟时间窗口
        window = int(time.time() // 300)
        key = f"active_users:{window}"
        
        await redis_manager.sadd(key, user_id)
        await redis_manager.expire(key, 600)  # 保留10分钟
    
    async def _get_daily_metric_count(self, metric_name: str) -> int:
        """获取每日指标计数"""
        from datetime import datetime
        today = datetime.now().strftime("%Y-%m-%d")
        key = f"metric:{metric_name}:{today}"
        
        count = await redis_manager.redis_client.scard(key)
        return count or 0


# API 性能追踪装饰器
def track_api_performance(endpoint: str):
    """API 性能追踪装饰器"""
    import functools
    
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                # 提取HTTP方法，默认为POST（因为大部分游戏API是POST）
                method = "POST"  # 简化处理，不从request中提取
                api_latency_histogram.labels(
                    endpoint=endpoint,
                    method=method
                ).observe(duration)
        return wrapper
    return decorator


# 创建全局分析服务实例
analytics_service = AnalyticsService()


# Prometheus metrics endpoint
async def get_prometheus_metrics():
    """获取 Prometheus 格式的指标"""
    return generate_latest()


# 监控告警检查
async def check_alerts():
    """检查告警阈值"""
    alerts = []
    
    # 检查 API 错误率
    # 检查数据库连接池
    # 检查 Redis 内存
    # 检查 DAU 下降
    
    return alerts 