"""
配置管理器模块
提供游戏配置的加载、缓存和热更新功能
"""
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# 全局配置缓存
_config_cache: Dict[str, Any] = {}
_config_last_modified: Dict[str, float] = {}

def get_config_path(config_name: str = "game_config") -> Path:
    """获取配置文件路径"""
    config_dir = Path(__file__).parent.parent.parent / "config"
    return config_dir / f"{config_name}.yaml"

def load_game_config(force_reload: bool = False) -> Dict[str, Any]:
    """
    加载游戏配置文件
    
    Args:
        force_reload: 是否强制重新加载，忽略缓存
        
    Returns:
        游戏配置字典
    """
    config_path = get_config_path("game_config")
    cache_key = "game_config"
    
    try:
        # 检查文件是否存在
        if not config_path.exists():
            logger.error(f"配置文件不存在: {config_path}")
            return _get_default_config()
        
        # 获取文件修改时间
        current_mtime = config_path.stat().st_mtime
        
        # 检查是否需要重新加载
        if (not force_reload and 
            cache_key in _config_cache and 
            cache_key in _config_last_modified and
            _config_last_modified[cache_key] >= current_mtime):
            logger.debug("使用缓存的配置数据")
            return _config_cache[cache_key]
        
        # 加载配置文件
        logger.info(f"加载配置文件: {config_path}")
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        if not config_data:
            logger.warning("配置文件为空，使用默认配置")
            return _get_default_config()
        
        # 验证配置格式
        validated_config = _validate_config(config_data)
        
        # 更新缓存
        _config_cache[cache_key] = validated_config
        _config_last_modified[cache_key] = current_mtime
        
        logger.info("配置文件加载成功")
        return validated_config
        
    except yaml.YAMLError as e:
        logger.error(f"YAML解析错误: {e}")
        return _get_default_config()
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return _get_default_config()

def _validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证配置格式并填充默认值
    
    Args:
        config: 原始配置数据
        
    Returns:
        验证后的配置数据
    """
    # 确保必要的配置节存在
    default_sections = {
        "currency": {},
        "hotspots": {},
        "tasks": {"daily": {"tasks": [], "reward_levels": []}},
        "login_rewards": {},
        "ads": {},
        "ai_interactions": {}
    }
    
    for section, default_value in default_sections.items():
        if section not in config:
            config[section] = default_value
            logger.warning(f"配置节 '{section}' 不存在，使用默认值")
    
    # 验证任务配置
    if "tasks" in config and "daily" in config["tasks"]:
        daily_config = config["tasks"]["daily"]
        if "tasks" not in daily_config:
            daily_config["tasks"] = []
        if "reward_levels" not in daily_config:
            daily_config["reward_levels"] = []
    
    return config

def _get_default_config() -> Dict[str, Any]:
    """获取默认配置"""
    return {

        "hotspots": {
            "min_per_session": 3,
            "max_per_session": 8,
            "types": {
                "thief": {
                    "probability": 0.6,
                    "rewards": {"experience": {"min": 10, "max": 15}}
                },
                "garbage": {
                    "probability": 0.3,
                    "rewards": {"experience": {"min": 5, "max": 10}}
                },
                "treasure": {
                    "probability": 0.08,
                    "rewards": {"experience": {"min": 15, "max": 25}}
                },
                "boss_thief": {
                    "probability": 0.02,
                    "rewards": {"experience": {"min": 50, "max": 100}},
                    "has_artifact_chance": 0.1
                }
            }
        },
        "tasks": {
            "daily": {
                "tasks": [
                    {
                        "id": "daily_login",
                        "name": "每日登录",
                        "description": "登录游戏",
                        "target": 1,
                        "type": "login",
                        "rewards": {"exp": 10}
                    },
                    {
                        "id": "collect_hotspots",
                        "name": "收集热点",
                        "description": "收集5个热点目标",
                        "target": 5,
                        "type": "collect",
                        "rewards": {"exp": 20}
                    },
                    {
                        "id": "defeat_boss",
                        "name": "击败BOSS",
                        "description": "击败1个垃圾大王BOSS",
                        "target": 1,
                        "type": "boss",
                        "rewards": {"exp": 30}
                    },
                    {
                        "id": "complete_sessions",
                        "name": "完成关卡",
                        "description": "完成3个游戏会话",
                        "target": 3,
                        "type": "session",
                        "rewards": {"exp": 20}
                    },
                    {
                        "id": "watch_ads",
                        "name": "观看广告",
                        "description": "观看2个广告",
                        "target": 2,
                        "type": "ad",
                        "rewards": {"exp": 20}
                    }
                ],
                "reward_levels": [
                ]
            }
        },
        "login_rewards": {},
        "ads": {},
        # 🚫 PRD合规性清理：移除lottery_wheel配置 - 转盘抽奖系统不在PRD要求中
        "ai_interactions": {}
    }

def clear_config_cache():
    """清除配置缓存"""
    global _config_cache, _config_last_modified
    _config_cache.clear()
    _config_last_modified.clear()
    logger.info("配置缓存已清除")

def get_config_info() -> Dict[str, Any]:
    """获取配置信息"""
    config_path = get_config_path("game_config")
    
    info = {
        "config_path": str(config_path),
        "exists": config_path.exists(),
        "cached": "game_config" in _config_cache,
        "cache_size": len(_config_cache)
    }
    
    if config_path.exists():
        stat = config_path.stat()
        info.update({
            "file_size": stat.st_size,
            "last_modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
            "last_modified_timestamp": stat.st_mtime
        })
    
    return info
