"""
热点奖励服务 - 处理概率控制的奖励逻辑
"""
import logging
import random
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, desc
import time

from app.models.hotspot_reward import HotspotRewardConfig, SessionRandomSeed, HotspotDropHistory
from app.models.user import User

logger = logging.getLogger(__name__)

class HotspotRewardService:
    """热点奖励服务"""

    async def create_session_seed(self, db: AsyncSession, session_id: str, user: User) -> SessionRandomSeed:
        """创建会话随机种子"""
        try:
            # 生成基于时间和用户ID的随机种子
            current_time = int(time.time() * 1000000)  # 微秒级时间戳
            seed = (current_time + user.id * 1000007) % (2**31 - 1)  # 大质数避免冲突
            
            session_seed = SessionRandomSeed(
                session_id=session_id,
                user_id=user.id,
                random_seed=seed
            )
            
            # 先检查是否已存在
            existing_result = await db.execute(
                select(SessionRandomSeed).where(SessionRandomSeed.session_id == session_id)
            )
            existing_seed = existing_result.scalar_one_or_none()
            
            if existing_seed:
                # 如果已存在，重置计数器
                existing_seed.thief_collected_count = 0
                existing_seed.garbage_collected_count = 0
                existing_seed.treasure_collected_count = 0
                existing_seed.boss_defeated_count = 0
                existing_seed.random_seed = seed
                await db.commit()
                return existing_seed
            else:
                # 如果不存在，创建新记录
                db.add(session_seed)
                await db.commit()
                await db.refresh(session_seed)
                return session_seed
            
        except Exception as e:
            logger.error(f"创建会话种子失败: {e}")
            await db.rollback()
            raise

    async def get_session_seed(self, db: AsyncSession, session_id: str) -> Optional[SessionRandomSeed]:
        """获取会话随机种子"""
        try:
            result = await db.execute(
                select(SessionRandomSeed).where(SessionRandomSeed.session_id == session_id)
            )
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取会话种子失败: {e}")
            return None

    async def get_hotspot_reward_configs(self, db: AsyncSession, hotspot_type: str) -> List[HotspotRewardConfig]:
        """获取热点奖励配置"""
        try:
            result = await db.execute(
                select(HotspotRewardConfig).where(
                    and_(
                        HotspotRewardConfig.hotspot_type == hotspot_type,
                        HotspotRewardConfig.is_enabled == True
                    )
                ).order_by(desc(HotspotRewardConfig.weight))
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"获取热点奖励配置失败: {e}")
            return []

    async def calculate_reward(
        self, 
        db: AsyncSession, 
        session_id: str, 
        user: User,
        hotspot_type: str, 
        hotspot_name: str
    ) -> Dict[str, Any]:
        """计算热点奖励"""
        try:
            # 1. 获取或创建会话种子
            session_seed = await self.get_session_seed(db, session_id)
            if not session_seed:
                logger.warning(f"会话种子不存在: {session_id}")
                return {"type": "experience", "amount": 0}

            # 2. 获取热点奖励配置
            configs = await self.get_hotspot_reward_configs(db, hotspot_type)
            if not configs:
                logger.warning(f"热点类型 {hotspot_type} 没有奖励配置")
                return {"type": "experience", "amount": 0}

            # 3. 为每个奖励类型生成随机数并判断掉落
            rewards = []
            
            for config in configs:
                # 获取基于种子的随机数
                random_value = session_seed.get_next_random_value(hotspot_type)
                drop_probability = float(config.base_drop_rate)
                
                # 判断是否掉落
                dropped = random_value <= drop_probability
                
                if dropped:
                    # 计算奖励数量（在min_amount和max_amount之间随机）
                    if config.max_amount > config.min_amount:
                        # 使用相同的随机数生成器确保一致性
                        rng = session_seed.get_seeded_random(session_seed.thief_collected_count + 1000)
                        amount = rng.randint(config.min_amount, config.max_amount)
                    else:
                        amount = config.min_amount
                    
                    rewards.append({
                        "type": config.reward_type,
                        "amount": amount,
                        "weight": config.weight
                    })
                
                # 记录掉落历史
                history = HotspotDropHistory(
                    session_id=session_id,
                    user_id=user.id,
                    hotspot_type=hotspot_type,
                    hotspot_name=hotspot_name,
                    reward_type=config.reward_type if dropped else None,
                    reward_amount=amount if dropped else 0,
                    drop_probability=drop_probability,
                    random_value=random_value,
                    dropped=dropped
                )
                db.add(history)

            # 4. 更新会话收集计数
            session_seed.increment_count(hotspot_type)
            
            await db.commit()

            # 5. 如果有多个奖励，根据权重选择一个主要奖励
            if rewards:
                # 按权重排序，返回权重最高的奖励
                rewards.sort(key=lambda x: x["weight"], reverse=True)
                main_reward = rewards[0]
                
                # 如果有多个奖励，合并同类型奖励
                combined_rewards = {}
                for reward in rewards:
                    reward_type = reward["type"]
                    if reward_type in combined_rewards:
                        combined_rewards[reward_type] += reward["amount"]
                    else:
                        combined_rewards[reward_type] = reward["amount"]
                
                # 返回主要奖励和所有奖励详情
                return {
                    "type": main_reward["type"],
                    "amount": combined_rewards[main_reward["type"]],
                    "all_rewards": combined_rewards
                }
            else:
                # 没有奖励掉落
                return {"type": "experience", "amount": 0}

        except Exception as e:
            logger.error(f"计算热点奖励失败: {e}")
            await db.rollback()
            return {"type": "experience", "amount": 0}

    async def get_hotspot_statistics(self, db: AsyncSession, user_id: Optional[int] = None) -> Dict[str, Any]:
        """获取热点掉落统计"""
        try:
            # 基础查询
            query = select(HotspotDropHistory)
            if user_id:
                query = query.where(HotspotDropHistory.user_id == user_id)

            result = await db.execute(query)
            histories = result.scalars().all()

            # 统计数据
            stats = {
                "total_collections": len(histories),
                "total_drops": len([h for h in histories if h.dropped]),
                "drop_rate": 0.0,
                "by_type": {},
                "by_reward": {}
            }

            # 计算总掉落率
            if stats["total_collections"] > 0:
                stats["drop_rate"] = stats["total_drops"] / stats["total_collections"]

            # 按热点类型统计
            for history in histories:
                hotspot_type = history.hotspot_type
                if hotspot_type not in stats["by_type"]:
                    stats["by_type"][hotspot_type] = {
                        "total": 0,
                        "dropped": 0,
                        "drop_rate": 0.0
                    }
                
                stats["by_type"][hotspot_type]["total"] += 1
                if history.dropped:
                    stats["by_type"][hotspot_type]["dropped"] += 1

            # 计算各类型掉落率
            for type_stats in stats["by_type"].values():
                if type_stats["total"] > 0:
                    type_stats["drop_rate"] = type_stats["dropped"] / type_stats["total"]

            # 按奖励类型统计
            for history in histories:
                if history.dropped and history.reward_type:
                    reward_type = history.reward_type
                    if reward_type not in stats["by_reward"]:
                        stats["by_reward"][reward_type] = {
                            "count": 0,
                            "total_amount": 0
                        }
                    
                    stats["by_reward"][reward_type]["count"] += 1
                    stats["by_reward"][reward_type]["total_amount"] += history.reward_amount

            return stats

        except Exception as e:
            logger.error(f"获取热点统计失败: {e}")
            return {}

    async def update_reward_config(
        self, 
        db: AsyncSession, 
        config_id: int, 
        updates: Dict[str, Any]
    ) -> bool:
        """更新奖励配置"""
        try:
            result = await db.execute(
                select(HotspotRewardConfig).where(HotspotRewardConfig.id == config_id)
            )
            config = result.scalar_one_or_none()
            
            if not config:
                return False

            # 更新配置
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)

            await db.commit()
            return True

        except Exception as e:
            logger.error(f"更新奖励配置失败: {e}")
            await db.rollback()
            return False

    async def create_reward_config(
        self, 
        db: AsyncSession, 
        config_data: Dict[str, Any]
    ) -> Optional[HotspotRewardConfig]:
        """创建新的奖励配置"""
        try:
            config = HotspotRewardConfig(**config_data)
            db.add(config)
            await db.commit()
            await db.refresh(config)
            return config

        except Exception as e:
            logger.error(f"创建奖励配置失败: {e}")
            await db.rollback()
            return None

    async def delete_reward_config(self, db: AsyncSession, config_id: int) -> bool:
        """删除奖励配置"""
        try:
            result = await db.execute(
                select(HotspotRewardConfig).where(HotspotRewardConfig.id == config_id)
            )
            config = result.scalar_one_or_none()
            
            if not config:
                return False

            await db.delete(config)
            await db.commit()
            return True

        except Exception as e:
            logger.error(f"删除奖励配置失败: {e}")
            await db.rollback()
            return False

# 创建全局实例
hotspot_reward_service = HotspotRewardService()