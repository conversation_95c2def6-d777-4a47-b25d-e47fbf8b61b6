"""
XML热点配置解析服务
用于解析XML场景文件中的热点数据并同步到数据库
"""
import xml.etree.ElementTree as ET
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from decimal import Decimal

from app.models.game import SceneHotspot, HotspotType, RewardType
from app.core.database import get_db

logger = logging.getLogger(__name__)


class XMLHotspotParser:
    """XML热点配置解析器"""
    
    def __init__(self):
        self.xml_scenes_path = Path("frontend/public/scenes")
        
    async def parse_xml_scene(self, scene_file_path: str) -> Dict:
        """
        解析XML场景文件，提取热点配置
        
        Args:
            scene_file_path: XML文件路径
            
        Returns:
            解析结果，包含热点列表和场景信息
        """
        try:
            scene_path = Path(scene_file_path)
            if not scene_path.exists():
                raise FileNotFoundError(f"场景文件不存在: {scene_file_path}")
            
            # 解析XML文件
            tree = ET.parse(scene_path)
            root = tree.getroot()
            
            # 提取场景信息
            scene_element = root.find('.//scene')
            if scene_element is None:
                raise ValueError("XML文件中未找到scene元素")
            
            scene_id = scene_element.get('name', 'unknown_scene')
            scene_title = scene_element.get('title', scene_id)
            
            # 从文件名推断城市ID
            city_id = self._extract_city_from_filename(scene_path.name)
            
            # 解析热点
            hotspots = []
            hotspot_elements = root.findall('.//hotspot')
            
            for hotspot_elem in hotspot_elements:
                hotspot_data = self._parse_hotspot_element(hotspot_elem)
                if hotspot_data:
                    hotspots.append(hotspot_data)
            
            logger.info(f"成功解析场景 {scene_id}，发现 {len(hotspots)} 个热点")
            
            return {
                "city_id": city_id,
                "scene_id": scene_id,
                "scene_title": scene_title,
                "hotspots": hotspots,
                "xml_file_path": str(scene_path)
            }
            
        except Exception as e:
            logger.error(f"解析XML文件失败 {scene_file_path}: {str(e)}")
            raise
    
    def _parse_hotspot_element(self, hotspot_elem: ET.Element) -> Optional[Dict]:
        """解析单个热点元素"""
        try:
            name = hotspot_elem.get('name')
            if not name:
                return None
            
            # 判断热点类型
            hotspot_type = self._determine_hotspot_type(name, hotspot_elem)
            if not hotspot_type:
                return None
            
            # 提取位置信息
            ath = float(hotspot_elem.get('ath', 0))
            atv = float(hotspot_elem.get('atv', 0))
            
            # 提取奖励信息（从onclick或其他属性推断）
            reward_data = self._extract_reward_from_hotspot(name, hotspot_type)
            
            return {
                "name": name,
                "type": hotspot_type,
                "position_x": Decimal(str(ath)),
                "position_y": Decimal(str(atv)),
                "reward_type": reward_data["type"],
                "reward_amount": reward_data["amount"],
                "reward_data": reward_data.get("data", {}),
                "scale": float(hotspot_elem.get('scale', 1.0)),
                "visible": hotspot_elem.get('visible', 'true').lower() == 'true',
                "url": hotspot_elem.get('url', ''),
                "onclick": hotspot_elem.get('onclick', '')
            }
            
        except Exception as e:
            logger.warning(f"解析热点元素失败: {str(e)}")
            return None
    
    def _determine_hotspot_type(self, name: str, hotspot_elem: ET.Element) -> Optional[str]:
        """根据名称和属性判断热点类型"""
        name_lower = name.lower()
        
        if 'thief' in name_lower:
            return 'thief'
        elif 'garbage' in name_lower or 'laji' in name_lower:
            return 'garbage'
        elif 'treasure' in name_lower:
            return 'treasure'
        elif 'boss' in name_lower:
            return 'boss_thief'
        
        # 根据URL判断
        url = hotspot_elem.get('url', '').lower()
        if 'thief' in url:
            return 'thief'
        elif 'laji' in url or 'garbage' in url:
            return 'garbage'
        elif 'treasure' in url:
            return 'treasure'
        
        return None
    
    def _extract_reward_from_hotspot(self, name: str, hotspot_type: str) -> Dict:
        """从热点信息中推断奖励数据"""
        # 根据热点类型设置默认奖励
        default_rewards = {
            'thief': {'type': 'treasure_box', 'amount': 0, 'data': {
                'contents': {'experience': {'min': 10, 'max': 20}, 'artifact': {'min': 0, 'max': 1}}
            }},
            'garbage': {'type': 'treasure_box', 'amount': 0, 'data': {
                'contents': {'experience': {'min': 5, 'max': 15}, 'artifact': {'min': 0, 'max': 1}}
            }},
            'treasure': {'type': 'treasure_box', 'amount': 0, 'data': {
                'contents': {'experience': {'min': 20, 'max': 40}, 'artifact': {'min': 1, 'max': 2}}
            }},
            'boss_thief': {'type': 'treasure_box', 'amount': 0, 'data': {
                'contents': {'experience': {'min': 50, 'max': 100}, 'artifact': {'min': 2, 'max': 5}}
            }}
        }
        
        return default_rewards.get(hotspot_type, {'type': 'experience', 'amount': 10, 'data': {}})
    
    def _extract_city_from_filename(self, filename: str) -> str:
        """从文件名中提取城市ID"""
        # 常见的城市映射
        city_mapping = {
            'level_1': 'beijing',
            'beijing': 'beijing',
            'shanghai': 'shanghai',
            'newyork': 'newyork',
            'london': 'london',
            'tokyo': 'tokyo'
        }
        
        filename_lower = filename.lower()
        for key, city in city_mapping.items():
            if key in filename_lower:
                return city
        
        return 'beijing'  # 默认城市
    
    async def sync_xml_to_database(self, db: AsyncSession, scene_file_path: str, 
                                 force_update: bool = False) -> Dict:
        """
        将XML配置同步到数据库
        
        Args:
            db: 数据库会话
            scene_file_path: XML文件路径
            force_update: 是否强制更新（删除现有数据）
            
        Returns:
            同步结果
        """
        try:
            # 解析XML文件
            parsed_data = await self.parse_xml_scene(scene_file_path)
            city_id = parsed_data["city_id"]
            scene_id = parsed_data["scene_id"]
            hotspots = parsed_data["hotspots"]
            
            # 如果强制更新，先删除现有数据
            if force_update:
                delete_stmt = delete(SceneHotspot).where(
                    SceneHotspot.city_id == city_id,
                    SceneHotspot.scene_id == scene_id
                )
                await db.execute(delete_stmt)
                await db.commit()
                logger.info(f"已删除 {city_id}/{scene_id} 的现有热点数据")
            
            # 检查现有热点
            existing_hotspots_result = await db.execute(
                select(SceneHotspot.hotspot_name).where(
                    SceneHotspot.city_id == city_id,
                    SceneHotspot.scene_id == scene_id
                )
            )
            existing_names = set(row[0] for row in existing_hotspots_result.fetchall())
            
            # 插入新热点
            new_hotspots = []
            updated_hotspots = []
            
            for hotspot_data in hotspots:
                hotspot_name = hotspot_data["name"]
                
                # 创建热点记录
                scene_hotspot = SceneHotspot(
                    city_id=city_id,
                    scene_id=scene_id,
                    hotspot_name=hotspot_name,
                    hotspot_type=hotspot_data["type"],
                    position_x=hotspot_data["position_x"],
                    position_y=hotspot_data["position_y"],
                    reward_type=hotspot_data["reward_type"],
                    reward_amount=hotspot_data["reward_amount"],
                    reward_data=hotspot_data["reward_data"]
                )
                
                if hotspot_name in existing_names:
                    # 更新现有热点
                    if not force_update:  # force_update时已删除，不需要更新
                        # 这里可以添加更新逻辑
                        updated_hotspots.append(hotspot_name)
                else:
                    # 添加新热点
                    db.add(scene_hotspot)
                    new_hotspots.append(hotspot_name)
            
            await db.commit()
            
            result = {
                "success": True,
                "city_id": city_id,
                "scene_id": scene_id,
                "total_hotspots": len(hotspots),
                "new_hotspots": len(new_hotspots),
                "updated_hotspots": len(updated_hotspots),
                "hotspot_details": {
                    "new": new_hotspots,
                    "updated": updated_hotspots
                }
            }
            
            logger.info(f"XML热点同步完成: {result}")
            return result
            
        except Exception as e:
            await db.rollback()
            logger.error(f"XML热点同步失败: {str(e)}")
            raise
    
    async def scan_and_sync_all_scenes(self, db: AsyncSession) -> List[Dict]:
        """扫描并同步所有场景文件"""
        results = []
        
        if not self.xml_scenes_path.exists():
            logger.warning(f"场景目录不存在: {self.xml_scenes_path}")
            return results
        
        # 查找所有XML文件
        xml_files = list(self.xml_scenes_path.glob("*.xml"))
        
        for xml_file in xml_files:
            try:
                logger.info(f"正在同步场景文件: {xml_file.name}")
                result = await self.sync_xml_to_database(db, str(xml_file))
                results.append(result)
            except Exception as e:
                logger.error(f"同步场景文件失败 {xml_file.name}: {str(e)}")
                results.append({
                    "success": False,
                    "file": str(xml_file),
                    "error": str(e)
                })
        
        return results


# 创建全局解析器实例
xml_hotspot_parser = XMLHotspotParser() 