"""
统一热点配置服务
支持全局默认 → 场景级别 → 精细化配置的继承体系
"""

import random
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import yaml

from app.core.config import GameConfig

logger = logging.getLogger(__name__)

class UnifiedHotspotService:
    """统一热点配置服务"""
    
    def __init__(self):
        # 加载统一配置文件
        config_path = Path(__file__).parent.parent.parent / "config" / "unified_hotspot_config.yaml"
        self.config = GameConfig(config_path)
        self._config_cache = {}
    
    def get_hotspot_configuration(self, city_id: str, scene_id: str, event_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取最终的热点配置（经过继承和优先级处理）
        
        Args:
            city_id: 城市ID
            scene_id: 场景ID  
            event_id: 可选的事件ID
            
        Returns:
            最终的热点配置
        """
        cache_key = f"{city_id}_{scene_id}_{event_id or 'normal'}"
        
        if cache_key in self._config_cache:
            return self._config_cache[cache_key]
        
        # 1. 获取全局默认配置
        global_defaults = self.config.get("global_defaults", {})
        
        # 2. 获取场景配置
        scene_config = self.config.get(f"scenes.{scene_id}", {})
        
        # 3. 获取城市配置
        city_config = self.config.get(f"cities.{city_id}", {})
        
        # 4. 获取事件配置（如果有）
        event_config = {}
        if event_id:
            event_config = self.config.get(f"events.{event_id}", {})
            if not event_config.get("active", False):
                event_config = {}
        
        # 5. 构建最终配置
        final_config = self._merge_configurations(
            global_defaults, scene_config, city_config, event_config
        )
        
        # 6. 缓存结果
        self._config_cache[cache_key] = final_config
        
        return final_config
    
    def _merge_configurations(self, global_defaults: Dict, scene_config: Dict, 
                            city_config: Dict, event_config: Dict) -> Dict[str, Any]:
        """合并配置，处理继承和优先级"""
        
        # 基础配置从全局默认开始
        final_config = {
            "global_defaults": global_defaults,
            "scene_info": scene_config.get("info", {}),
            "hotspot_types": {},
            "precise_hotspots": {},
            "generation_mode": "batch"
        }
        
        # 场景级别默认配置（覆盖全局默认）
        scene_defaults = scene_config.get("scene_defaults", {})
        merged_rewards = self._merge_reward_configs(
            global_defaults.get("rewards", {}),
            scene_defaults.get("rewards", {})
        )
        
        # 事件配置覆盖（如果有）
        if event_config:
            event_overrides = event_config.get("global_overrides", {})
            if "rewards" in event_overrides:
                merged_rewards = self._merge_reward_configs(
                    merged_rewards,
                    event_overrides["rewards"]
                )
        
        # 处理热点类型配置
        hotspot_types = scene_config.get("hotspot_types", {})
        for hotspot_type, type_config in hotspot_types.items():
            if not type_config.get("enabled", True):
                continue
            
            # 获取该类型的最终奖励配置
            final_reward = self._get_final_reward_config(
                hotspot_type, merged_rewards, type_config
            )
            
            # 获取位置配置
            final_position = self._get_final_position_config(
                global_defaults.get("position", {}),
                type_config
            )
            
            final_config["hotspot_types"][hotspot_type] = {
                **type_config,
                "final_reward": final_reward,
                "final_position": final_position
            }
        
        # 处理精确热点配置（最高优先级）
        precise_hotspots = scene_config.get("precise_hotspots", {})
        
        # 添加事件特殊热点
        if event_config:
            special_hotspots = event_config.get("special_hotspots", {})
            precise_hotspots.update(special_hotspots)
        
        final_config["precise_hotspots"] = precise_hotspots
        
        # 应用城市倍数
        final_config = self._apply_city_multipliers(final_config, city_config)
        
        return final_config
    
    def _merge_reward_configs(self, base_rewards: Dict, override_rewards: Dict) -> Dict:
        """合并奖励配置"""
        merged = base_rewards.copy() if base_rewards else {}
        if override_rewards:
            for reward_type, config in override_rewards.items():
                merged[reward_type] = config
        return merged
    
    def _get_final_reward_config(self, hotspot_type: str, merged_rewards: Dict, 
                               type_config: Dict) -> Dict[str, Any]:
        """获取最终的奖励配置"""
        
        # 优先级：type_config.reward_override > merged_rewards[hotspot_type]
        if "reward_override" in type_config:
            return type_config["reward_override"]
        
        return merged_rewards.get(hotspot_type, {
            "type": "experience",
            "amount_range": {"min": 10, "max": 20}
        })
    
    def _get_final_position_config(self, global_position: Dict, type_config: Dict) -> Dict[str, Any]:
        """获取最终的位置配置"""
        
        # 优先级：type_config.position_override > global_position
        if "position_override" in type_config:
            return type_config["position_override"]
        
        return global_position
    
    def _apply_city_multipliers(self, config: Dict, city_config: Dict) -> Dict:
        """应用城市特定的倍数配置"""
        
        difficulty_multiplier = city_config.get("difficulty_multiplier", 1.0)
        reward_multipliers = city_config.get("reward_multipliers", {})
        
        # 应用倍数到热点类型配置
        hotspot_types = config.get("hotspot_types", {}) or {}
        for hotspot_type, type_config in hotspot_types.items():
            final_reward = type_config["final_reward"]
            reward_type = final_reward.get("type", "experience")
            
            # 应用奖励类型倍数
            type_multiplier = reward_multipliers.get(reward_type, 1.0)
            
            # 应用总体难度倍数
            total_multiplier = difficulty_multiplier * type_multiplier
            
            if total_multiplier != 1.0:
                amount_range = final_reward.get("amount_range", {})
                if amount_range:
                    final_reward["amount_range"] = {
                        "min": int(amount_range.get("min", 0) * total_multiplier),
                        "max": int(amount_range.get("max", 0) * total_multiplier)
                    }
        
        # 应用倍数到精确热点配置
        precise_hotspots = config.get("precise_hotspots", {}) or {}
        for hotspot_name, hotspot_config in precise_hotspots.items():
            if "reward" in hotspot_config:
                reward = hotspot_config["reward"]
                reward_type = reward.get("type", "gold")
                type_multiplier = reward_multipliers.get(reward_type, 1.0)
                total_multiplier = difficulty_multiplier * type_multiplier
                
                if total_multiplier != 1.0 and "amount" in reward:
                    reward["amount"] = int(reward["amount"] * total_multiplier)
        
        return config
    
    def generate_hotspot_reward(self, hotspot_type: str, hotspot_name: str, 
                              config: Dict[str, Any]) -> Dict[str, Any]:
        # 1. 检查精确配置（最高优先级）
        precise_hotspots = config.get("precise_hotspots", {}) or {}
        if hotspot_name in precise_hotspots:
            precise_config = precise_hotspots[hotspot_name]
            if "reward" in precise_config:
                reward = precise_config["reward"]
                return self._generate_reward_from_config(reward)
        
        # 2. 使用类型配置
        hotspot_types = config.get("hotspot_types", {}) or {}
        if hotspot_type in hotspot_types:
            type_config = hotspot_types[hotspot_type]
            final_reward = type_config.get("final_reward", {})
            
            return self._generate_reward_from_config(final_reward)
        
        # 3. 默认奖励
        return {"type": "experience", "amount": 10}
    
    def _generate_reward_from_config(self, reward_config: Dict[str, Any]) -> Dict[str, Any]:
        """从配置生成奖励"""
        reward_type = reward_config.get("type", "experience")
        
        if reward_type == "treasure_box":
            # 宝箱奖励：包含多种资源
            contents_config = reward_config.get("contents", {})
            contents = {}
            
            for resource_type, resource_range in contents_config.items():
                if isinstance(resource_range, dict) and "min" in resource_range and "max" in resource_range:
                    min_amount = resource_range["min"]
                    max_amount = resource_range["max"]
                    amount = random.randint(min_amount, max_amount)
                    if amount > 0:  # 只包含非零奖励
                        contents[resource_type] = amount
                elif isinstance(resource_range, (int, float)):
                    # 固定数量
                    if resource_range > 0:
                        contents[resource_type] = int(resource_range)
            
            return {
                "type": "treasure_box",
                "contents": contents
            }
        
        else:
            # 单一资源奖励
            amount_range = reward_config.get("amount_range", {"min": 10, "max": 20})
            
            if isinstance(amount_range, dict) and "min" in amount_range and "max" in amount_range:
                min_amount = amount_range.get("min", 10)
                max_amount = amount_range.get("max", 20)
                reward_amount = random.randint(min_amount, max_amount)
            else:
                reward_amount = 10
            
            return {
                "type": reward_type,
                "amount": reward_amount
            }
    
    def generate_hotspot_position(self, hotspot_type: str, hotspot_name: str,
                                config: Dict[str, Any], used_positions: List[Dict] = None) -> Dict[str, float]:
        """
        生成热点位置
        
        Args:
            hotspot_type: 热点类型
            hotspot_name: 热点名称
            config: 最终配置
            used_positions: 已使用的位置列表
            
        Returns:
            {"x": x, "y": y}
        """
        used_positions = used_positions or []
        
        # 1. 检查精确配置（最高优先级）
        precise_hotspots = config.get("precise_hotspots", {}) or {}
        if hotspot_name in precise_hotspots:
            precise_config = precise_hotspots[hotspot_name]
            if "position" in precise_config:
                return precise_config["position"]
        
        # 2. 使用类型配置
        hotspot_types = config.get("hotspot_types", {}) or {}
        if hotspot_type in hotspot_types:
            type_config = hotspot_types[hotspot_type]
            final_position = type_config.get("final_position", {})
            
            return self._generate_position_by_config(final_position, used_positions, hotspot_name)
        
        # 3. 默认随机位置
        return {"x": random.uniform(-180, 180), "y": random.uniform(-90, 90)}
    
    def _generate_position_by_config(self, position_config: Dict, used_positions: List[Dict], 
                                   hotspot_name: str) -> Dict[str, float]:
        """根据位置配置生成位置"""
        
        mode = position_config.get("mode", "random")
        
        if mode == "fixed":
            fixed_pos = position_config.get("fixed_position", {"x": 0, "y": 0})
            return fixed_pos
        
        elif mode == "predefined":
            predefined_positions = position_config.get("predefined_positions", [])
            if predefined_positions:
                # 基于名称选择位置（确保一致性）
                import hashlib
                name_hash = int(hashlib.md5(hotspot_name.encode()).hexdigest(), 16)
                position_index = name_hash % len(predefined_positions)
                return predefined_positions[position_index]
        
        elif mode == "random":
            random_area = position_config.get("random_area", {
                "x_min": -180, "x_max": 180, "y_min": -90, "y_max": 90
            })
            avoid_overlap = position_config.get("avoid_overlap", {})
            
            return self._generate_random_position(random_area, avoid_overlap, used_positions)
        
        # 默认随机位置
        return {"x": random.uniform(-180, 180), "y": random.uniform(-90, 90)}
    
    def _generate_random_position(self, random_area: Dict, avoid_overlap: Dict, 
                                used_positions: List[Dict]) -> Dict[str, float]:
        """生成随机位置"""
        
        x_min = random_area.get("x_min", -180)
        x_max = random_area.get("x_max", 180)
        y_min = random_area.get("y_min", -90)
        y_max = random_area.get("y_max", 90)
        
        max_attempts = 50
        min_distance = avoid_overlap.get("min_distance", 0.01) if avoid_overlap.get("enabled", False) else 0
        
        for _ in range(max_attempts):
            x = random.uniform(x_min, x_max)
            y = random.uniform(y_min, y_max)
            
            # 检查与已有位置的距离
            if min_distance > 0:
                too_close = False
                for used_pos in used_positions:
                    distance = ((x - used_pos["x"]) ** 2 + (y - used_pos["y"]) ** 2) ** 0.5
                    if distance < min_distance:
                        too_close = True
                        break
                
                if too_close:
                    continue
            
            return {"x": x, "y": y}
        
        # 如果无法找到合适位置，返回随机位置
        return {"x": random.uniform(x_min, x_max), "y": random.uniform(y_min, y_max)}
    
    def get_available_events(self) -> List[str]:
        """获取可用的事件列表"""
        events = self.config.get("events", {})
        return [event_id for event_id, event_config in events.items() 
                if event_config.get("active", False)]
    
    def reload_config(self):
        """重新加载配置"""
        self.config.reload()
        self._config_cache.clear()
        logger.info("统一热点配置已重新加载")

# 全局实例
unified_hotspot_service = UnifiedHotspotService() 