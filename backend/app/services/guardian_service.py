"""
守护等级系统服务
处理文化图鉴收集经验、等级升级、特殊能力解锁等功能
基于PRD要求：通过收集文化图鉴获得经验值，自动升级守护者等级
"""
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.models.user import User
from app.models.passive_income import GuardianLevel, GuardianUpgradeRecord
from app.core.config import game_config

logger = logging.getLogger(__name__)


class GuardianService:
    """守护等级管理服务"""
    
    def __init__(self):
        # 从配置文件读取守护等级配置
        guardian_config = game_config.get("guardian_system", {})
        self.artifact_experience = guardian_config.get("artifact_experience", {
            "bronze": 1,
            "silver": 5,
            "gold": 20
        })
        self.level_bonuses = guardian_config.get("level_bonuses", [])
        
        logger.info(f"守护等级服务初始化: 图鉴经验 {self.artifact_experience}")
    
    def _calculate_level_requirement(self, level: int) -> int:
        """计算升级到指定等级所需的经验值"""
        guardian_config = game_config.get("guardian_system", {})
        formula = guardian_config.get("experience_per_level_formula", "10 * level")
        
        # 简单解析公式: "10 * level"
        if "level" in formula:
            return eval(formula.replace("level", str(level)))
        return 10 * level
    
    def _calculate_experience_requirement(self, level: int) -> int:
        """计算升级到指定等级所需的经验值"""
        guardian_config = game_config.get("guardian_system", {})
        experience_config = guardian_config.get("experience_requirement", {})

        # PRD要求：守护者等级通过文化经验值自动升级，不需要货币
        base_exp = experience_config.get("base_exp", 100)
        multiplier = experience_config.get("level_multiplier", 1.2)

        # 计算所需经验值
        required_exp = int(base_exp * (multiplier ** (level - 1)))

        return required_exp
    
    def _get_unlocked_abilities(self, level: int) -> List[str]:
        """获取指定等级解锁的特殊能力"""
        abilities = []
        for bonus in self.level_bonuses:
            if level >= bonus["level"]:
                abilities.append(bonus["effect"])
        return abilities
    
    async def get_or_create_guardian_level(
        self,
        db: AsyncSession,
        user: User
    ) -> GuardianLevel:
        """获取或创建用户的守护等级记录"""
        try:
            # 尝试获取现有记录
            result = await db.execute(
                select(GuardianLevel).where(GuardianLevel.user_id == user.id)
            )
            guardian_level = result.scalar_one_or_none()
            
            if not guardian_level:
                # 创建新的守护等级记录
                guardian_level = GuardianLevel(
                    user_id=user.id,
                    level=0,
                    experience=0,
                    total_experience=0,
                    unlocked_abilities=json.dumps([])
                )
                db.add(guardian_level)
                await db.commit()
                await db.refresh(guardian_level)
                logger.info(f"为用户{user.id}创建守护等级记录")
            
            return guardian_level
            
        except Exception as e:
            logger.error(f"获取守护等级记录失败: {e}")
            raise
    
    async def add_artifact_experience(
        self,
        db: AsyncSession,
        user: User,
        artifact_rarity: str,
        count: int = 1
    ) -> Dict[str, Any]:
        """
        添加文化图鉴收集经验

        Args:
            db: 数据库会话
            user: 用户对象
            artifact_rarity: 文化图鉴稀有度 (bronze, silver, gold)
            count: 图鉴数量

        Returns:
            文化经验添加结果
        """
        try:
            guardian_level = await self.get_or_create_guardian_level(db, user)
            
            # 计算获得的经验
            exp_per_artifact = self.artifact_experience.get(artifact_rarity, 0)
            total_exp_gained = exp_per_artifact * count
            
            if total_exp_gained <= 0:
                return {
                    "success": False,
                    "message": f"未知的图鉴稀有度: {artifact_rarity}"
                }
            
            # 更新图鉴统计
            if artifact_rarity == "bronze":
                guardian_level.bronze_artifacts += count
            elif artifact_rarity == "silver":
                guardian_level.silver_artifacts += count
            elif artifact_rarity == "gold":
                guardian_level.gold_artifacts += count
            
            guardian_level.total_artifacts += count
            
            # 添加经验
            old_level = guardian_level.level
            old_experience = guardian_level.experience
            
            guardian_level.experience += total_exp_gained
            guardian_level.total_experience += total_exp_gained
            
            # 检查升级
            level_ups = 0
            new_abilities = []
            
            while True:
                next_level = guardian_level.level + 1
                required_exp = self._calculate_level_requirement(next_level)
                
                if guardian_level.experience >= required_exp:
                    guardian_level.experience -= required_exp
                    guardian_level.level += 1
                    level_ups += 1
                    
                    # 检查新解锁的能力
                    abilities = self._get_unlocked_abilities(guardian_level.level)
                    current_abilities = json.loads(guardian_level.unlocked_abilities or "[]")
                    for ability in abilities:
                        if ability not in current_abilities:
                            current_abilities.append(ability)
                            new_abilities.append(ability)
                    guardian_level.unlocked_abilities = json.dumps(current_abilities)
                else:
                    break
            
            await db.commit()
            
            logger.info(f"用户{user.id}获得图鉴经验: {artifact_rarity}x{count} -> {total_exp_gained}经验, 升级{level_ups}级")
            
            return {
                "success": True,
                "artifact_rarity": artifact_rarity,
                "artifact_count": count,
                "exp_gained": total_exp_gained,
                "old_level": old_level,
                "new_level": guardian_level.level,
                "level_ups": level_ups,
                "current_experience": guardian_level.experience,
                "new_abilities": new_abilities,
                "total_artifacts": guardian_level.total_artifacts
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"添加图鉴经验失败: {e}")
            return {"error": f"添加图鉴经验失败: {str(e)}"}
    
    async def get_guardian_info(
        self,
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """获取用户守护等级信息"""
        try:
            guardian_level = await self.get_or_create_guardian_level(db, user)
            
            # 计算下一级所需经验
            next_level_req = self._calculate_level_requirement(guardian_level.level + 1)
            
            # 获取已解锁的能力
            current_abilities = json.loads(guardian_level.unlocked_abilities or "[]")
            
            # 计算下一级所需经验值
            next_level_exp_req = self._calculate_experience_requirement(guardian_level.level + 1)

            return {
                "success": True,
                "level": guardian_level.level,
                "experience": guardian_level.experience,
                "total_experience": guardian_level.total_experience,
                "next_level_experience": next_level_req,
                "next_level_experience_requirement": next_level_exp_req,
                "artifacts": {
                    "bronze": guardian_level.bronze_artifacts,
                    "silver": guardian_level.silver_artifacts,
                    "gold": guardian_level.gold_artifacts,
                    "total": guardian_level.total_artifacts
                },
                "unlocked_abilities": current_abilities,
                "created_at": guardian_level.created_at.isoformat(),
                "updated_at": guardian_level.updated_at.isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取守护等级信息失败: {e}")
            return {"error": f"获取守护等级信息失败: {str(e)}"}
    
    async def upgrade_guardian_level(
        self,
        db: AsyncSession,
        user: User,
        use_currency: str = "deprecated"
    ) -> Dict[str, Any]:
        """
        守护等级升级接口（PRD合规版本）

        注意：根据PRD要求，守护等级通过收集文化图鉴自动升级，
        不再支持货币手动升级。此接口保留仅为API兼容性。
        """
        try:
            guardian_level = await self.get_or_create_guardian_level(db, user)
            
            # 计算升级花费
            next_level = guardian_level.level + 1
            upgrade_cost = self._calculate_upgrade_cost(next_level)
            
            # PRD合规性：守护者等级通过文化经验值自动提升，无需货币购买
            return {
                "success": False,
                "message": "PRD合规性：守护者等级通过收集文化图鉴和经验值自动提升，无需货币购买",
                "prd_compliance": True,
                "current_level": guardian_level.level,
                "current_experience": guardian_level.total_experience,
                "next_level_requirement": self._calculate_experience_requirement(next_level)
            }
            
            # 注意：以下代码不会被执行，因为上面已经返回了PRD合规性提示
            # 保留此处代码仅为向后兼容，实际系统使用文化经验值自动升级
            
        except Exception as e:
            await db.rollback()
            logger.error(f"手动升级守护等级失败: {e}")
            return {"error": f"手动升级守护等级失败: {str(e)}"}
    
    async def get_upgrade_history(
        self,
        db: AsyncSession,
        user: User,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取升级历史记录"""
        try:
            result = await db.execute(
                select(GuardianUpgradeRecord)
                .where(GuardianUpgradeRecord.user_id == user.id)
                .order_by(GuardianUpgradeRecord.created_at.desc())
                .limit(limit)
            )
            records = result.scalars().all()
            
            return [
                {
                    "from_level": record.from_level,
                    "to_level": record.to_level,
                    "experience_gained": record.experience_gained,
                    "experience_source": record.experience_source,
                    "new_abilities": json.loads(record.new_abilities or "[]"),
                    "created_at": record.created_at.isoformat()
                }
                for record in records
            ]
            
        except Exception as e:
            logger.error(f"获取升级历史失败: {e}")
            return []


# 创建全局服务实例
guardian_service = GuardianService()