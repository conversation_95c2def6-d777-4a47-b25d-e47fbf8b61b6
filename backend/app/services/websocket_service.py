"""
WebSocket服务 - 用于H5游戏和小程序之间的实时通信
"""
import json
import logging
import asyncio
from typing import Dict, Set, Optional, Any
from datetime import datetime
from fastapi import WebSocket, WebSocketDisconnect
from enum import Enum

logger = logging.getLogger(__name__)


class ClientType(Enum):
    """客户端类型"""
    H5 = "h5"
    MINIPROGRAM = "miniprogram"


class MessageType(Enum):
    """消息类型"""
    # 连接管理
    CONNECT = "connect"
    DISCONNECT = "disconnect"
    HEARTBEAT = "heartbeat"
    
    # 游戏状态
    GAME_START = "game_start"
    GAME_END = "game_end"
    GAME_PAUSE = "game_pause"
    GAME_RESUME = "game_resume"
    
    # 游戏数据
    HOTSPOT_COLLECT = "hotspot_collect"
    BOSS_ATTACK = "boss_attack"
    SCORE_UPDATE = "score_update"
    LEVEL_UPDATE = "level_update"
    RESOURCE_UPDATE = "resource_update"
    
    # 小程序特定
    MINIPROGRAM_COMMAND = "miniprogram_command"
    H5_RESPONSE = "h5_response"


class WebSocketConnection:
    """WebSocket连接对象"""
    def __init__(self, websocket: WebSocket, user_id: str, client_type: ClientType):
        self.websocket = websocket
        self.user_id = user_id
        self.client_type = client_type
        self.connected_at = datetime.now()
        self.last_heartbeat = datetime.now()
    
    async def send_message(self, message: Dict[str, Any]):
        """发送消息"""
        try:
            await self.websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
    
    async def close(self):
        """关闭连接"""
        try:
            await self.websocket.close()
        except Exception as e:
            logger.error(f"关闭连接失败: {e}")


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储用户的连接: {user_id: {client_type: WebSocketConnection}}
        self.user_connections: Dict[str, Dict[ClientType, WebSocketConnection]] = {}
        # 存储所有活跃连接
        self.active_connections: Set[WebSocketConnection] = set()
        
    async def connect(self, websocket: WebSocket, user_id: str, client_type: ClientType):
        """建立WebSocket连接"""
        await websocket.accept()
        
        connection = WebSocketConnection(websocket, user_id, client_type)
        
        # 如果用户已存在，更新连接
        if user_id not in self.user_connections:
            self.user_connections[user_id] = {}
        
        # 如果该用户的该类型客户端已连接，关闭旧连接
        if client_type in self.user_connections[user_id]:
            old_connection = self.user_connections[user_id][client_type]
            await old_connection.close()
            self.active_connections.discard(old_connection)
        
        self.user_connections[user_id][client_type] = connection
        self.active_connections.add(connection)
        
        logger.info(f"用户 {user_id} 的 {client_type.value} 客户端已连接")
        
        # 发送连接确认消息
        await connection.send_message({
            "type": MessageType.CONNECT.value,
            "status": "success",
            "client_type": client_type.value,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat()
        })
        
        return connection
    
    async def disconnect(self, connection: WebSocketConnection):
        """断开连接"""
        if connection in self.active_connections:
            self.active_connections.remove(connection)
            
            # 从用户连接字典中移除
            user_id = connection.user_id
            client_type = connection.client_type
            
            if user_id in self.user_connections:
                if client_type in self.user_connections[user_id]:
                    del self.user_connections[user_id][client_type]
                
                # 如果用户没有任何连接，删除用户记录
                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
            
            logger.info(f"用户 {user_id} 的 {client_type.value} 客户端已断开")
    
    async def send_to_user(self, user_id: str, message: Dict[str, Any], target_client: Optional[ClientType] = None):
        """发送消息给指定用户"""
        if user_id not in self.user_connections:
            logger.warning(f"用户 {user_id} 没有活跃连接")
            return False
        
        user_conns = self.user_connections[user_id]
        
        # 如果指定了目标客户端类型
        if target_client:
            if target_client in user_conns:
                await user_conns[target_client].send_message(message)
                return True
            else:
                logger.warning(f"用户 {user_id} 的 {target_client.value} 客户端未连接")
                return False
        
        # 发送给所有连接的客户端
        success = False
        for connection in user_conns.values():
            await connection.send_message(message)
            success = True
        
        return success
    
    async def send_to_miniprogram(self, user_id: str, message: Dict[str, Any]):
        """发送消息给小程序"""
        return await self.send_to_user(user_id, message, ClientType.MINIPROGRAM)
    
    async def send_to_h5(self, user_id: str, message: Dict[str, Any]):
        """发送消息给H5游戏"""
        return await self.send_to_user(user_id, message, ClientType.H5)
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """广播消息给所有连接"""
        for connection in self.active_connections:
            await connection.send_message(message)
    
    async def handle_message(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理接收到的消息"""
        try:
            message_type = message.get("type")
            user_id = connection.user_id
            
            logger.info(f"收到来自 {user_id} ({connection.client_type.value}) 的消息: {message_type}")
            
            if message_type == MessageType.HEARTBEAT.value:
                # 处理心跳消息
                connection.last_heartbeat = datetime.now()
                await connection.send_message({
                    "type": MessageType.HEARTBEAT.value,
                    "status": "ok",
                    "timestamp": datetime.now().isoformat()
                })
                return
            
            # 根据消息类型处理
            if message_type == MessageType.GAME_START.value:
                await self._handle_game_start(connection, message)
            elif message_type == MessageType.HOTSPOT_COLLECT.value:
                await self._handle_hotspot_collect(connection, message)
            elif message_type == MessageType.BOSS_ATTACK.value:
                await self._handle_boss_attack(connection, message)
            elif message_type == MessageType.SCORE_UPDATE.value:
                await self._handle_score_update(connection, message)
            elif message_type == MessageType.RESOURCE_UPDATE.value:
                await self._handle_resource_update(connection, message)
            elif message_type == MessageType.MINIPROGRAM_COMMAND.value:
                await self._handle_miniprogram_command(connection, message)
            else:
                logger.warning(f"未知消息类型: {message_type}")
        
        except Exception as e:
            logger.error(f"处理消息失败: {e}")
            await connection.send_message({
                "type": "error",
                "message": f"处理消息失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_game_start(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理游戏开始消息"""
        game_data = message.get("data", {})
        
        # 如果是H5发起的游戏开始，通知小程序
        if connection.client_type == ClientType.H5:
            await self.send_to_miniprogram(connection.user_id, {
                "type": MessageType.GAME_START.value,
                "data": game_data,
                "timestamp": datetime.now().isoformat()
            })
    
    async def _handle_hotspot_collect(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理热点收集消息"""
        hotspot_data = message.get("data", {})
        
        # 通知小程序热点收集事件
        await self.send_to_miniprogram(connection.user_id, {
            "type": MessageType.HOTSPOT_COLLECT.value,
            "data": {
                "hotspot_name": hotspot_data.get("hotspot_name"),
                "hotspot_type": hotspot_data.get("hotspot_type"),
                "reward": hotspot_data.get("reward"),
                "current_score": hotspot_data.get("current_score"),
                "timestamp": datetime.now().isoformat()
            }
        })
    
    async def _handle_boss_attack(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理BOSS攻击消息"""
        attack_data = message.get("data", {})
        
        await self.send_to_miniprogram(connection.user_id, {
            "type": MessageType.BOSS_ATTACK.value,
            "data": {
                "damage": attack_data.get("damage"),
                "is_critical": attack_data.get("is_critical"),
                "boss_health": attack_data.get("boss_health"),
                "is_killed": attack_data.get("is_killed"),
                "rewards": attack_data.get("rewards"),
                "timestamp": datetime.now().isoformat()
            }
        })
    
    async def _handle_score_update(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理分数更新消息"""
        score_data = message.get("data", {})
        
        await self.send_to_miniprogram(connection.user_id, {
            "type": MessageType.SCORE_UPDATE.value,
            "data": score_data,
            "timestamp": datetime.now().isoformat()
        })
    
    async def _handle_resource_update(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理资源更新消息"""
        resource_data = message.get("data", {})
        
        await self.send_to_miniprogram(connection.user_id, {
            "type": MessageType.RESOURCE_UPDATE.value,
            "data": {
                "level": resource_data.get("level"),
                "timestamp": datetime.now().isoformat()
            }
        })
    
    async def _handle_miniprogram_command(self, connection: WebSocketConnection, message: Dict[str, Any]):
        """处理小程序命令消息"""
        command_data = message.get("data", {})
        
        # 转发给H5游戏
        await self.send_to_h5(connection.user_id, {
            "type": MessageType.MINIPROGRAM_COMMAND.value,
            "data": command_data,
            "timestamp": datetime.now().isoformat()
        })
    
    def get_user_connections(self, user_id: str) -> Dict[ClientType, WebSocketConnection]:
        """获取用户的所有连接"""
        return self.user_connections.get(user_id, {})
    
    def get_active_users(self) -> Set[str]:
        """获取所有活跃用户ID"""
        return set(self.user_connections.keys())
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        total_connections = len(self.active_connections)
        h5_connections = sum(1 for conn in self.active_connections if conn.client_type == ClientType.H5)
        miniprogram_connections = sum(1 for conn in self.active_connections if conn.client_type == ClientType.MINIPROGRAM)
        
        return {
            "total_connections": total_connections,
            "h5_connections": h5_connections,
            "miniprogram_connections": miniprogram_connections,
            "active_users": len(self.user_connections),
            "timestamp": datetime.now().isoformat()
        }


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()