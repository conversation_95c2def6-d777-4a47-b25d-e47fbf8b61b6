"""
被动收益服务
"""
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.models.user import User
from app.models.passive_income import PassiveIncomeRecord
from app.core.config import game_config
from app.core.redis_client import redis_manager

logger = logging.getLogger(__name__)


class PassiveIncomeService:
    """被动收益管理服务"""
    
    def __init__(self):
        # 从配置文件读取被动收益配置
        passive_config = game_config.get("passive_income", {})
        self.base_experience_per_hour = passive_config.get("base_experience_per_hour", 50)
        # 优化：每次最多领取1小时收益，避免长时间累积
        self.max_accumulation_hours = passive_config.get("max_accumulation_hours", 1)
        self.guardian_bonus_rate = passive_config.get("guardian_level_bonus_rate", 0.05)

        logger.info(f"被动收益服务初始化: 基础{self.base_experience_per_hour}经验值/小时")
    
    async def calculate_offline_income(
        self,
        db: AsyncSession,
        user: User
    ) -> Dict[str, Any]:
        """
        计算离线收益
        
        Args:
            db: 数据库会话
            user: 用户对象
            
        Returns:
            离线收益信息
        """
        try:
            # 获取用户守护等级，暂时使用用户基础等级
            # TODO: 稍后集成完整的守护等级系统
            guardian_level = 0
            
            # 计算收益加成：每级增加5%
            bonus_multiplier = 1 + (guardian_level * self.guardian_bonus_rate)

            # 实际每小时收益
            actual_experience_per_hour = int(self.base_experience_per_hour * bonus_multiplier)
            
            # 获取上次领取时间
            last_collect_key = f"passive_income_last_collect:{user.id}"
            last_collect_time_str = await redis_manager.get(last_collect_key)
            
            current_time = datetime.utcnow()
            
            if last_collect_time_str:
                last_collect_time = datetime.fromisoformat(last_collect_time_str)
            else:
                # 首次计算，从用户最后登录时间开始
                last_collect_time = user.last_login_time or current_time
            
            # 计算离线时长（小时）
            time_diff = current_time - last_collect_time
            offline_hours = time_diff.total_seconds() / 3600
            
            # 限制最大积累时间（优化：每次最多1小时收益）
            effective_hours = min(offline_hours, self.max_accumulation_hours)
            
            # 计算收益
            experience_earned = int(actual_experience_per_hour * effective_hours)
            
            # 检查是否有特殊能力（守护等级10：自动领取）
            auto_collect = guardian_level >= 10
            
            # 优化：移除48小时累积能力，保持1小时上限激励频繁登录
            max_hours = self.max_accumulation_hours
            
            return {
                "offline_hours": round(offline_hours, 2),
                "effective_hours": round(effective_hours, 2),
                "max_accumulation_hours": max_hours,
                "experience_earned": experience_earned,
                "experience_per_hour": actual_experience_per_hour,
                "guardian_level": guardian_level,
                "bonus_multiplier": round(bonus_multiplier, 2),
                "auto_collect": auto_collect,
                "last_collect_time": last_collect_time.isoformat(),
                "can_collect": experience_earned > 0
            }
            
        except Exception as e:
            logger.error(f"计算离线收益失败: {e}")
            return {"error": f"计算离线收益失败: {str(e)}"}
    
    async def collect_passive_income(
        self,
        db: AsyncSession,
        user: User,
        watch_ad: bool = False
    ) -> Dict[str, Any]:
        """
        领取被动收益
        
        Args:
            db: 数据库会话
            user: 用户对象
            watch_ad: 是否观看广告获得双倍收益
            
        Returns:
            领取结果
        """
        try:
            # 先计算当前收益
            income_info = await self.calculate_offline_income(db, user)
            
            if "error" in income_info:
                return income_info
            
            if not income_info.get("can_collect", False):
                return {
                    "success": False,
                    "message": "暂无可领取的被动收益",
                    "income_info": income_info
                }
            
            experience_earned = income_info["experience_earned"]

            # 广告加成（守护等级50时广告奖励翻倍）
            guardian_level = income_info.get("guardian_level", 0)
            ad_multiplier = 2.0
            if guardian_level >= 50 and watch_ad:
                ad_multiplier = 4.0  # 双倍基础上再翻倍
            elif watch_ad:
                ad_multiplier = 2.0
            else:
                ad_multiplier = 1.0

            final_experience = int(experience_earned * ad_multiplier)
            
            # 更新用户经验值
            user.guardian_exp = (user.guardian_exp or 0) + final_experience
            
            # 记录收益历史
            income_record = PassiveIncomeRecord(
                user_id=user.id,
                offline_hours=income_info["effective_hours"],
                experience_earned=final_experience,
                base_experience_per_hour=income_info["experience_per_hour"],
                guardian_level=guardian_level,
                bonus_multiplier=income_info["bonus_multiplier"],
                ad_multiplier=ad_multiplier,
                watched_ad=watch_ad
            )
            db.add(income_record)
            
            # 更新最后领取时间
            current_time = datetime.utcnow()
            last_collect_key = f"passive_income_last_collect:{user.id}"
            await redis_manager.set(last_collect_key, current_time.isoformat(), expire=86400 * 7)
            
            await db.commit()
            
            logger.info(f"用户{user.id}领取被动收益: {final_experience}经验值 (广告加成{ad_multiplier}倍)")

            return {
                "success": True,
                "experience_earned": final_experience,
                "base_experience": experience_earned,
                "ad_multiplier": ad_multiplier,
                "watched_ad": watch_ad,
                "offline_hours": income_info["effective_hours"],
                "new_total_experience": user.guardian_exp,
                "income_info": income_info
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"领取被动收益失败: {e}")
            return {"error": f"领取被动收益失败: {str(e)}"}
    
    async def get_income_statistics(
        self,
        db: AsyncSession,
        user: User,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        获取被动收益统计信息
        
        Args:
            db: 数据库会话
            user: 用户对象
            days: 统计天数
            
        Returns:
            统计信息
        """
        try:
            # 获取指定天数内的收益记录
            start_date = datetime.utcnow() - timedelta(days=days)
            
            result = await db.execute(
                select(PassiveIncomeRecord)
                .where(
                    PassiveIncomeRecord.user_id == user.id,
                    PassiveIncomeRecord.created_at >= start_date
                )
                .order_by(PassiveIncomeRecord.created_at.desc())
            )
            records = result.scalars().all()
            
            # 计算统计数据
            total_experience = sum(getattr(record, 'experience_earned', 0) for record in records)
            total_hours = sum(record.offline_hours for record in records)
            total_collections = len(records)
            ad_collections = sum(1 for record in records if record.watched_ad)

            # 平均收益
            avg_experience_per_collection = total_experience / total_collections if total_collections > 0 else 0
            
            # 当前被动收益状态
            current_income = await self.calculate_offline_income(db, user)
            
            return {
                "period_days": days,
                "total_collections": total_collections,
                "total_experience_earned": total_experience,
                "total_offline_hours": round(total_hours, 2),
                "ad_enhanced_collections": ad_collections,
                "ad_usage_rate": round(ad_collections / total_collections * 100, 1) if total_collections > 0 else 0,
                "average_per_collection": {
                    "experience": round(avg_experience_per_collection, 1)
                },
                "current_income_status": current_income,
                "recent_records": [
                    {
                        "experience_earned": getattr(record, 'experience_earned', 0),
                        "offline_hours": round(record.offline_hours, 2),
                        "ad_multiplier": record.ad_multiplier,
                        "time": record.created_at.isoformat()
                    }
                    for record in records[:10]  # 最近10条记录
                ]
            }
            
        except Exception as e:
            logger.error(f"获取被动收益统计失败: {e}")
            return {"error": f"获取被动收益统计失败: {str(e)}"}


# 创建全局服务实例
passive_income_service = PassiveIncomeService()