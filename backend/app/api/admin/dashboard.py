"""
管理系统仪表板API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, desc, and_
from datetime import datetime, timedelta
import logging

from app.core.database import get_db
from app.models.user import User
from app.models.game import GameSession
from app.services.admin_auth_service import admin_auth_service

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/stats", summary="获取仪表板统计数据")
async def get_dashboard_stats(
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取仪表板统计数据
    """
    try:
        # 总用户数
        total_users_result = await db.execute(
            func.count(User.id)
        )
        total_users = total_users_result.scalar() or 0
        
        # 今日新增用户
        today = datetime.now().date()
        today_users_result = await db.execute(
            func.count(User.id).filter(
                func.date(User.created_at) == today
            )
        )
        today_users = today_users_result.scalar() or 0
        
        # 活跃用户（7天内有游戏会话）
        seven_days_ago = datetime.now() - timedelta(days=7)
        active_users_result = await db.execute(
            func.count(func.distinct(GameSession.user_id)).filter(
                GameSession.started_at >= seven_days_ago
            )
        )
        active_users = active_users_result.scalar() or 0

        # 总游戏会话数
        total_sessions_result = await db.execute(
            func.count(GameSession.id)
        )
        total_sessions = total_sessions_result.scalar() or 0

        # 今日游戏会话数
        today_sessions_result = await db.execute(
            func.count(GameSession.id).filter(
                func.date(GameSession.started_at) == today
            )
        )
        today_sessions = today_sessions_result.scalar() or 0
        
       
        
        return {
            "total_users": total_users,
            "today_users": today_users,
            "active_users": active_users,
            "total_sessions": total_sessions,
            "today_sessions": today_sessions,
            "user_growth_rate": round((today_users / max(total_users - today_users, 1)) * 100, 2),
            "session_growth_rate": round((today_sessions / max(total_sessions - today_sessions, 1)) * 100, 2)
        }
        
    except Exception as e:
        logger.error(f"获取仪表板统计数据失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取统计数据失败"
        )


@router.get("/charts/user-growth", summary="获取用户增长图表数据")
async def get_user_growth_chart(
    days: int = Query(7, description="天数"),
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取用户增长图表数据
    """
    try:
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        # 查询每日新增用户数
        daily_users = []
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            
            users_result = await db.execute(
                func.count(User.id).filter(
                    func.date(User.created_at) == current_date
                )
            )
            user_count = users_result.scalar() or 0
            
            daily_users.append({
                "date": current_date.isoformat(),
                "users": user_count
            })
        
        return {
            "data": daily_users,
            "total_days": days
        }
        
    except Exception as e:
        logger.error(f"获取用户增长图表数据失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取图表数据失败"
        )


@router.get("/charts/revenue", summary="获取收入图表数据")
async def get_revenue_chart(
    days: int = Query(7, description="天数"),
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取收入图表数据（模拟数据）
    """
    try:
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        # 模拟收入数据
        daily_revenue = []
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            
            # 基于用户活动模拟收入
            sessions_result = await db.execute(
                func.count(GameSession.id).filter(
                    func.date(GameSession.created_at) == current_date
                )
            )
            session_count = sessions_result.scalar() or 0
            
            # 模拟收入计算：每个会话平均收入0.1-0.5美元
            revenue = session_count * 0.3
            
            daily_revenue.append({
                "date": current_date.isoformat(),
                "revenue": round(revenue, 2)
            })
        
        return {
            "data": daily_revenue,
            "total_days": days,
            "currency": "USD"
        }
        
    except Exception as e:
        logger.error(f"获取收入图表数据失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取图表数据失败"
        )


@router.get("/activity", summary="获取游戏活动统计")
async def get_game_activity(
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取游戏活动统计
    """
    try:
        # 最近24小时的活动
        last_24h = datetime.now() - timedelta(hours=24)
        
        # 24小时内的会话数
        sessions_24h_result = await db.execute(
            func.count(GameSession.id).filter(
                GameSession.started_at >= last_24h
            )
        )
        sessions_24h = sessions_24h_result.scalar() or 0

        # 24小时内的活跃用户数
        active_users_24h_result = await db.execute(
            func.count(func.distinct(GameSession.user_id)).filter(
                GameSession.started_at >= last_24h
            )
        )
        active_users_24h = active_users_24h_result.scalar() or 0
        
        # 平均会话时长（分钟）
        avg_session_duration = 15  # 模拟数据
        
        return {
            "sessions_24h": sessions_24h,
            "active_users_24h": active_users_24h,
            "avg_session_duration": avg_session_duration,
            "peak_hours": [14, 15, 16, 20, 21, 22],  # 模拟高峰时段
            "total_playtime_hours": sessions_24h * (avg_session_duration / 60)
        }
        
    except Exception as e:
        logger.error(f"获取游戏活动统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取活动统计失败"
        )


@router.get("/top-players", summary="获取顶级玩家")
async def get_top_players(
    limit: int = Query(10, description="返回数量"),
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取顶级玩家
    """
    try:
        # 🚫 PRD合规性清理：改为查询经验最多的玩家 - PRD中没有货币概念
        from sqlalchemy import select
        top_players_result = await db.execute(
            select(User)
            .order_by(desc(User.guardian_exp))
            .limit(limit)
        )

        top_players = []
        for user in top_players_result.scalars():
            top_players.append({
                "user_id": user.user_id,
                "nickname": user.nickname,
                "level": user.level,
                "experience": user.exp,
                "created_at": user.created_at.isoformat() if user.created_at else None
            })
        
        return {
            "players": top_players,
            "total": len(top_players)
        }
        
    except Exception as e:
        logger.error(f"获取顶级玩家失败: {e}")
        raise HTTPException(
            status_code=500,
            detail="获取顶级玩家失败"
        )


@router.get("/health", summary="获取系统健康状态")
async def get_system_health(
    db: AsyncSession = Depends(get_db),
    current_admin = Depends(admin_auth_service.get_current_admin)
):
    """
    获取系统健康状态
    """
    try:
        # 检查数据库连接
        await db.execute("SELECT 1")
        
        # 模拟系统状态
        return {
            "database": "healthy",
            "redis": "healthy",
            "api": "healthy",
            "uptime": "99.9%",
            "response_time": "120ms",
            "memory_usage": "65%",
            "cpu_usage": "45%",
            "disk_usage": "30%"
        }
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {e}")
        return {
            "database": "error",
            "redis": "unknown",
            "api": "error",
            "uptime": "unknown",
            "response_time": "timeout",
            "memory_usage": "unknown",
            "cpu_usage": "unknown",
            "disk_usage": "unknown"
        }
