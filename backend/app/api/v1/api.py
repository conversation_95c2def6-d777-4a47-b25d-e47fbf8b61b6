"""
API v1 路由聚合
"""
from fastapi import APIRouter

from app.api.v1.endpoints.auth import router as auth_router
from app.api.v1.endpoints.game import router as game_router

from app.api.v1.endpoints.task import router as task_router
from app.api.v1.endpoints.user import router as user_router
from app.api.v1.endpoints.health import router as health_router
from app.api.v1.endpoints.config import router as config_router

from app.api.v1.endpoints.advertisement import router as advertisement_router
from app.api.v1.endpoints.passive_income import router as passive_income_router
from app.api.v1.endpoints.hotspot_sync import router as hotspot_sync_router
# 🚫 PRD合规性清理：移除非PRD合规的路由导入
# from app.api.v1.endpoints.lottery import router as lottery_router  # 转盘抽奖 - 非PRD要求
# from app.api.v1.endpoints.websocket import router as websocket_router  # WebSocket - 非PRD要求
# from app.api.v1.endpoints.admin_monitoring import router as admin_monitoring_router  # 管理员监控 - 非PRD要求


api_router = APIRouter()

# 认证相关
api_router.include_router(
    auth_router,
    prefix="/auth",
    tags=["认证"]
)

# 游戏核心
api_router.include_router(
    game_router,
    prefix="/game",
    tags=["游戏"]
)

# 热点同步系统
api_router.include_router(
    hotspot_sync_router,
    prefix="/hotspots",
    tags=["热点同步"]
)

# 配置管理
api_router.include_router(
    config_router,
    prefix="/config",
    tags=["配置管理"]
)



# 任务系统
api_router.include_router(
    task_router,
    prefix="/task",
    tags=["任务"]
)

# 用户信息
api_router.include_router(
    user_router,
    prefix="/user",
    tags=["用户"]
)



# 广告系统
api_router.include_router(
    advertisement_router,
    prefix="/ads",
    tags=["广告系统"]
)

# 🚫 PRD合规性清理：移除非PRD合规的API端点
# 以下系统不在PRD要求中，已被移除：
# - 转盘抽奖系统（PRD中无抽奖机制）
# - WebSocket实时通信（PRD中无实时通信要求）
# - 管理员监控系统（PRD中无管理员功能要求）

# 被动收益系统 - PRD合规（守护者等级被动收益）
api_router.include_router(
    passive_income_router,
    prefix="/passive",
    tags=["被动收益"]
)

# 经验值系统
from app.api.v1.endpoints.experience import router as experience_router
api_router.include_router(
    experience_router,
    prefix="/experience",
    tags=["经验值系统"]
)

# 守护者系统（PRD核心系统）
from app.api.v1.endpoints.guardian_system import router as guardian_system_router
api_router.include_router(
    guardian_system_router,
    prefix="/guardian",
    tags=["守护者系统"]
)

# 每日任务系统
from app.api.v1.endpoints.daily_tasks import router as daily_tasks_router
api_router.include_router(
    daily_tasks_router,
    prefix="/tasks",
    tags=["每日任务系统"]
)

# 宝箱系统
from app.api.v1.endpoints.treasure_box import router as treasure_box_router
api_router.include_router(
    treasure_box_router,
    prefix="/treasure",
    tags=["宝箱系统"]
)

# 文化问答系统
from app.api.v1.endpoints.cultural_quiz import router as cultural_quiz_router
api_router.include_router(
    cultural_quiz_router,
    prefix="/culture",
    tags=["文化问答系统"]
)

# 健康检查
api_router.include_router(
    health_router,
    tags=["健康检查"]
)

