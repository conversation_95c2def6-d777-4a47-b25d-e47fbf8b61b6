"""
热点配置API - 游戏端
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional
import logging

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.models.game import SceneHotspot

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/scene/{scene_id}")
async def get_scene_hotspots(
    scene_id: str,
    city_id: Optional[str] = Query(None, description="城市ID"),
    enabled_only: bool = Query(True, description="只返回启用的热点"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取场景热点配置
    
    Args:
        scene_id: 场景ID
        city_id: 城市ID（可选）
        enabled_only: 是否只返回启用的热点
        
    Returns:
        热点配置列表
    """
    try:
        # 构建查询
        query = select(SceneHotspot).where(SceneHotspot.scene_id == scene_id)
        
        if city_id:
            query = query.where(SceneHotspot.city_id == city_id)
        
        if enabled_only:
            query = query.where(SceneHotspot.enabled == True)
        
        # 按创建时间排序
        query = query.order_by(SceneHotspot.created_at)
        
        result = await db.execute(query)
        hotspots = result.scalars().all()
        
        # 转换为前端需要的格式
        hotspot_data = []
        for hotspot in hotspots:
            hotspot_dict = {
                "id": hotspot.id,
                "name": hotspot.hotspot_name,
                "type": hotspot.hotspot_type.value if hotspot.hotspot_type else "thief",
                "position": {
                    "ath": float(hotspot.position_x) if hotspot.position_x else 0,
                    "atv": float(hotspot.position_y) if hotspot.position_y else 0
                },
                "scale": float(hotspot.scale) if hotspot.scale else 1.0,
                "image_url": hotspot.image_url or "",
                "visible": hotspot.visible,
                "enabled": hotspot.enabled,
                "reward": {
                    "type": hotspot.reward_type.value if hotspot.reward_type else "experience",
                    "amount": hotspot.reward_amount or 0,
                    "data": hotspot.reward_data or {}
                },
                "onclick_action": hotspot.onclick_action or "",
                "description": hotspot.description or ""
            }
            hotspot_data.append(hotspot_dict)
        
        return {
            "success": True,
            "data": {
                "scene_id": scene_id,
                "city_id": city_id,
                "hotspots": hotspot_data,
                "total_count": len(hotspot_data)
            }
        }
        
    except Exception as e:
        logger.error(f"获取场景热点失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取热点配置失败: {str(e)}")


@router.post("/validate")
async def validate_hotspot_config(
    config_data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    验证热点配置是否有效
    
    Args:
        config_data: 热点配置数据
        
    Returns:
        验证结果
    """
    try:
        errors = []
        warnings = []
        
        # 验证必需字段
        required_fields = ["scene_id", "hotspot_name", "hotspot_type", "position", "reward"]
        for field in required_fields:
            if field not in config_data:
                errors.append(f"缺少必需字段: {field}")
        
        # 验证位置信息
        if "position" in config_data:
            position = config_data["position"]
            if not isinstance(position, dict):
                errors.append("position必须是对象")
            else:
                if "ath" not in position or "atv" not in position:
                    errors.append("position必须包含ath和atv字段")
                else:
                    try:
                        ath = float(position["ath"])
                        atv = float(position["atv"])
                        
                        # 检查坐标范围
                        if not (-180 <= ath <= 180):
                            warnings.append("ath坐标超出正常范围(-180到180)")
                        if not (-90 <= atv <= 90):
                            warnings.append("atv坐标超出正常范围(-90到90)")
                            
                    except (ValueError, TypeError):
                        errors.append("ath和atv必须是有效的数字")
        
        # 验证热点类型
        if "hotspot_type" in config_data:
            valid_types = ["thief", "garbage", "treasure", "boss_thief"]
            if config_data["hotspot_type"] not in valid_types:
                errors.append(f"无效的热点类型，支持的类型: {', '.join(valid_types)}")
        
        # 验证奖励信息
        if "reward" in config_data:
            reward = config_data["reward"]
            if not isinstance(reward, dict):
                errors.append("reward必须是对象")
            else:
                if "type" not in reward:
                    errors.append("reward必须包含type字段")
                else:
                    valid_reward_types = ["experience", "artifact", "treasure_box"]
                    if reward["type"] not in valid_reward_types:
                        errors.append(f"无效的奖励类型，支持的类型: {', '.join(valid_reward_types)}")
                
                if "amount" in reward:
                    try:
                        amount = int(reward["amount"])
                        if amount < 0:
                            warnings.append("奖励数量不应为负数")
                    except (ValueError, TypeError):
                        errors.append("奖励数量必须是有效的整数")
        
        # 验证缩放比例
        if "scale" in config_data:
            try:
                scale = float(config_data["scale"])
                if scale <= 0:
                    warnings.append("缩放比例应大于0")
                elif scale > 5:
                    warnings.append("缩放比例过大，可能影响显示效果")
            except (ValueError, TypeError):
                errors.append("缩放比例必须是有效的数字")
        
        # 检查热点名称重复
        if "scene_id" in config_data and "hotspot_name" in config_data:
            existing_query = select(SceneHotspot).where(
                SceneHotspot.scene_id == config_data["scene_id"],
                SceneHotspot.hotspot_name == config_data["hotspot_name"]
            )
            result = await db.execute(existing_query)
            existing_hotspot = result.scalar_one_or_none()
            
            if existing_hotspot:
                warnings.append("该场景中已存在同名热点")
        
        return {
            "success": True,
            "data": {
                "is_valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "validated_config": config_data
            }
        }
        
    except Exception as e:
        logger.error(f"验证热点配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"验证失败: {str(e)}")


@router.get("/preview/{scene_id}")
async def preview_scene_hotspots(
    scene_id: str,
    city_id: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    预览场景热点配置（用于调试和测试）
    
    Args:
        scene_id: 场景ID
        city_id: 城市ID（可选）
        
    Returns:
        热点预览数据
    """
    try:
        # 获取所有热点（包括禁用的）
        query = select(SceneHotspot).where(SceneHotspot.scene_id == scene_id)
        
        if city_id:
            query = query.where(SceneHotspot.city_id == city_id)
        
        result = await db.execute(query)
        hotspots = result.scalars().all()
        
        # 统计信息
        stats = {
            "total": len(hotspots),
            "enabled": sum(1 for h in hotspots if h.enabled),
            "disabled": sum(1 for h in hotspots if not h.enabled),
            "by_type": {}
        }
        
        # 按类型统计
        for hotspot in hotspots:
            hotspot_type = hotspot.hotspot_type.value if hotspot.hotspot_type else "unknown"
            if hotspot_type not in stats["by_type"]:
                stats["by_type"][hotspot_type] = 0
            stats["by_type"][hotspot_type] += 1
        
        # 生成预览数据
        preview_data = []
        for hotspot in hotspots:
            preview_item = {
                "id": hotspot.id,
                "name": hotspot.hotspot_name,
                "type": hotspot.hotspot_type.value if hotspot.hotspot_type else "thief",
                "position": f"({hotspot.position_x}, {hotspot.position_y})",
                "scale": float(hotspot.scale) if hotspot.scale else 1.0,
                "enabled": hotspot.enabled,
                "visible": hotspot.visible,
                "reward": f"{hotspot.reward_type.value if hotspot.reward_type else 'experience'} x{hotspot.reward_amount or 0}",
                "created_at": hotspot.created_at.isoformat() if hotspot.created_at else None
            }
            preview_data.append(preview_item)
        
        return {
            "success": True,
            "data": {
                "scene_id": scene_id,
                "city_id": city_id,
                "stats": stats,
                "hotspots": preview_data
            }
        }
        
    except Exception as e:
        logger.error(f"预览场景热点失败: {e}")
        raise HTTPException(status_code=500, detail=f"预览失败: {str(e)}")


@router.get("/export/krpano/{scene_id}")
async def export_krpano_xml(
    scene_id: str,
    city_id: Optional[str] = Query(None),
    enabled_only: bool = Query(True),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    导出krpano XML格式的热点配置
    
    Args:
        scene_id: 场景ID
        city_id: 城市ID（可选）
        enabled_only: 是否只导出启用的热点
        
    Returns:
        krpano XML格式的热点配置
    """
    try:
        # 获取热点数据
        query = select(SceneHotspot).where(SceneHotspot.scene_id == scene_id)
        
        if city_id:
            query = query.where(SceneHotspot.city_id == city_id)
        
        if enabled_only:
            query = query.where(SceneHotspot.enabled == True)
        
        result = await db.execute(query)
        hotspots = result.scalars().all()
        
        # 生成XML内容
        xml_lines = []
        xml_lines.append('<!-- 热点配置 - 由管理后台生成 -->')
        
        for hotspot in hotspots:
            # 构建onclick事件
            onclick_action = hotspot.onclick_action
            if not onclick_action:
                # 根据类型生成默认的onclick事件
                type_actions = {
                    "thief": f"js(onThiefClicked('{hotspot.hotspot_name}'));",
                    "garbage": f"js(onGarbageClicked('{hotspot.hotspot_name}'));",
                    "treasure": f"js(onTreasureClicked('{hotspot.hotspot_name}'));",
                    "boss_thief": f"js(onBossThiefClicked('{hotspot.hotspot_name}'));"
                }
                hotspot_type = hotspot.hotspot_type.value if hotspot.hotspot_type else "thief"
                onclick_action = type_actions.get(hotspot_type, f"js(onHotspotClicked('{hotspot.hotspot_name}'));")
            
            # 生成hotspot XML
            hotspot_xml = f'''        <hotspot name="{hotspot.hotspot_name}" 
                 url="{hotspot.image_url or ''}" 
                 ath="{hotspot.position_x}" 
                 atv="{hotspot.position_y}" 
                 scale="{hotspot.scale or 1.0}" 
                 onclick="{onclick_action}" 
                 visible="{str(hotspot.visible).lower()}"
                 data-type="{hotspot.hotspot_type.value if hotspot.hotspot_type else 'thief'}"
                 data-reward-type="{hotspot.reward_type.value if hotspot.reward_type else 'experience'}"
                 data-reward-amount="{hotspot.reward_amount or 0}" />'''
            
            xml_lines.append(hotspot_xml)
        
        xml_content = '\n'.join(xml_lines)
        
        return {
            "success": True,
            "data": {
                "scene_id": scene_id,
                "city_id": city_id,
                "xml_content": xml_content,
                "hotspot_count": len(hotspots)
            }
        }
        
    except Exception as e:
        logger.error(f"导出krpano XML失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")
