"""
被动收益API端点
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.services.passive_income_service import passive_income_service
from app.services.guardian_service import guardian_service

router = APIRouter()


@router.get("/status", response_model=Dict[str, Any])
async def get_passive_income_status(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取被动收益状态"""
    try:
        income_info = await passive_income_service.calculate_offline_income(db, current_user)
        return {
            "success": True,
            "data": income_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取被动收益状态失败: {str(e)}")


@router.post("/collect", response_model=Dict[str, Any])
async def collect_passive_income(
    watch_ad: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """领取被动收益"""
    try:
        result = await passive_income_service.collect_passive_income(
            db, current_user, watch_ad=watch_ad
        )
        
        if "error" in result:
            raise HTTPException(status_code=400, detail=result["error"])
        
        return {
            "success": True,
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"领取被动收益失败: {str(e)}")


@router.get("/statistics", response_model=Dict[str, Any])
async def get_passive_income_statistics(
    days: int = 7,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取被动收益统计"""
    try:
        stats = await passive_income_service.get_income_statistics(db, current_user, days)
        
        if "error" in stats:
            raise HTTPException(status_code=500, detail=stats["error"])
        
        return {
            "success": True,
            "data": stats
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取被动收益统计失败: {str(e)}")


@router.get("/guardian/info", response_model=Dict[str, Any])
async def get_guardian_info(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取守护等级信息"""
    try:
        guardian_info = await guardian_service.get_guardian_info(db, current_user)
        
        if "error" in guardian_info:
            raise HTTPException(status_code=500, detail=guardian_info["error"])
        
        return {
            "success": True,
            "data": guardian_info
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取守护等级信息失败: {str(e)}")


@router.post("/guardian/upgrade", response_model=Dict[str, Any])
async def upgrade_guardian_level(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """手动升级守护等级（通过经验值自动升级）"""
    try:
        # 🚫 PRD合规性清理：移除货币升级逻辑，PRD中守护等级通过经验值自动提升
        result = await guardian_service.check_level_up(db, current_user)

        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])

        return {
            "success": True,
            "data": result,
            "message": "守护等级通过经验值自动提升，无需手动升级"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"守护等级升级失败: {str(e)}")


@router.get("/guardian/history", response_model=Dict[str, Any])
async def get_guardian_upgrade_history(
    limit: int = 20,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取守护等级升级历史"""
    try:
        history = await guardian_service.get_upgrade_history(db, current_user, limit)
        
        return {
            "success": True,
            "data": {
                "history": history,
                "total_records": len(history)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取升级历史失败: {str(e)}")


@router.post("/guardian/add_artifact_experience", response_model=Dict[str, Any])
async def add_artifact_experience(
    artifact_rarity: str,  # bronze, silver, gold
    count: int = 1,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """添加图鉴收集经验（主要用于测试和管理）"""
    try:
        if artifact_rarity not in ["bronze", "silver", "gold"]:
            raise HTTPException(status_code=400, detail="图鉴稀有度必须是 'bronze', 'silver' 或 'gold'")
        
        if count <= 0:
            raise HTTPException(status_code=400, detail="数量必须大于0")
        
        result = await guardian_service.add_artifact_experience(
            db, current_user, artifact_rarity, count
        )
        
        if "error" in result:
            raise HTTPException(status_code=500, detail=result["error"])
        
        if not result.get("success", False):
            raise HTTPException(status_code=400, detail=result.get("message", "添加经验失败"))
        
        return {
            "success": True,
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加图鉴经验失败: {str(e)}")