"""
游戏核心API端点
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any
from pathlib import Path
import yaml
from datetime import datetime

logger = logging.getLogger(__name__)

from app.core.database import get_db
from app.core.config import hotspot_templates_config
from app.api.deps import get_current_user, rate_limit_session_start
# 🚫 PRD合规性清理：移除rate_limit_boss_attack，PRD中没有BOSS攻击
from app.models.user import User
from app.schemas.game import (
    SessionStartRequest, SessionStartResponse,
    CollectHotspotRequest, CollectHotspotResponse,
    EndSessionResponse,
    HotspotImportRequest, HotspotExportResponse, HotspotTemplateListResponse,
    # 🚫 PRD合规性清理：移除BossAttackRequest, BossAttackResponse - PRD中没有BOSS攻击
)
from app.services.game_service import game_service
from app.services.config_service import config_service
from app.services.collection_cache_service import collection_cache_service
from app.services.experience_cache_service import experience_cache_service
from app.analytics.metrics import track_api_performance

router = APIRouter()


@router.post("/session/start", response_model=SessionStartResponse)
@track_api_performance("/game/session/start")
async def start_session(
    request: SessionStartRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: bool = Depends(rate_limit_session_start)
):
    """
    开始游戏会话

    - 创建新的游戏会话
    - 返回用户当前状态和收集进度
    - 热点数据由前端从XML文件加载
    """
    result = await game_service.create_session(
        db=db,
        user=current_user,
        city_id=request.city_id,
        scene_id=request.scene_id
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.post("/session/{session_id}/collect", response_model=CollectHotspotResponse)
@track_api_performance("/game/session/collect")
async def collect_hotspot(
    session_id: str,
    request: CollectHotspotRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    收集热点
    
    - 验证热点有效性
    - 发放奖励（金币/钻石）
    - 更新收集进度
    """
    result = await game_service.collect_hotspot(
        db=db,
        user=current_user,
        session_id=session_id,
        hotspot_id=request.hotspot_id
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.post("/session/{session_id}/clear-cache")
@track_api_performance("/game/session/clear-cache")
async def clear_session_cache(
    session_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    清理会话缓存

    - 清理Redis中的热点收集状态
    - 解决"热点已收集"的重复收集问题
    - 仅限开发和调试使用
    """
    # 清理经验缓存服务中的热点状态（主要使用的服务）
    exp_result = await experience_cache_service.clear_session_hotspots(session_id)

    # 清理收集缓存服务中的状态（备用）
    collection_result = await collection_cache_service.clear_session_cache(session_id)

    total_cleared = (exp_result.get("cleared_keys", 0) +
                    collection_result.get("cleared_keys", 0))

    return {
        "success": True,
        "message": f"会话 {session_id} 缓存已清理",
        "cleared_keys": total_cleared,
        "details": {
            "experience_cache": exp_result.get("cleared_keys", 0),
            "collection_cache": collection_result.get("cleared_keys", 0)
        }
    }


# 🚫 PRD合规性清理：移除BOSS攻击端点
# PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少
# 相关功能已在guardian_system.py中的收集行为端点实现


@router.post("/session/{session_id}/end", response_model=EndSessionResponse)
@track_api_performance("/game/session/end")
async def end_session(
    session_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    结束游戏会话
    
    - 结算奖励
    - 计算经验值
    - 检查升级
    - 返回会话总结
    """
    result = await game_service.end_session(
        db=db,
        user=current_user,
        session_id=session_id
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


# 🚫 PRD合规性清理：移除复杂大炮系统
# PRD要求简单弹药系统，不需要复杂的大炮升级机制
# 以下大炮相关接口已被移除以符合PRD要求

# 🚫 PRD合规性清理：删除弹药状态端点 - PRD中没有弹药概念





# 🚫 PRD合规性清理：移除复杂大炮升级系统
# PRD要求简单弹药系统，不需要复杂的大炮升级机制


@router.get("/cities")
@track_api_performance("/game/cities")
async def get_cities(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取城市列表

    返回所有可用的城市信息，包括：
    - 城市基本信息
    - 解锁状态
    - 场景配置
    """
    try:
        # 从配置服务获取所有城市配置
        cities = await config_service.get_all_cities(db)

        # 处理城市数据，添加用户相关信息
        result = []
        for city in cities:
            city_data = {
                "city_id": city.get("city_id"),
                "name": city.get("name"),
                "unlock_requirement": city.get("unlock_requirement", "default"),
                "total_artifacts": city.get("total_artifacts", 16),
                "scene_config": city.get("scene_config", {}),
                "is_unlocked": city.get("city_id") == "beijing"  # 默认只有北京解锁
            }
            result.append(city_data)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get cities: {str(e)}"
        )


# ===== 热点模板管理 API =====

@router.post("/hotspots/import")
@track_api_performance("/game/hotspots/import")
async def import_hotspots_template(
    request: HotspotImportRequest,
    current_user: User = Depends(get_current_user)
):
    """
    导入热点模板
    
    - 支持JSON格式的热点布局导入
    - 可配置预设、混合或随机生成模式
    - 支持热点编辑器生成的数据
    """
    # 转换请求数据为JSON格式
    template_data = {
        "generation_mode": request.generation_mode,
        "hotspots": [
            {
                "name": hotspot.name,
                "type": hotspot.type,
                "position": {
                    "x": hotspot.position.x,
                    "y": hotspot.position.y
                },
                "reward": {
                    "type": hotspot.reward.type,
                    "amount": hotspot.reward.amount
                }
            }
            for hotspot in request.hotspots
        ]
    }
    
    # 添加混合模式配置
    if request.generation_mode == "mixed":
        if request.preset_count is not None:
            template_data["preset_count"] = request.preset_count
        if request.random_count is not None:
            template_data["random_count"] = request.random_count
    
    success = await game_service.import_hotspots_from_json(
        json_data=template_data,
        city_id=request.city_id,
        scene_id=request.scene_id
    )
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to import hotspots template"
        )
    
    return {"success": True, "message": "Hotspots template imported successfully"}


@router.get("/hotspots/export/{city_id}/{scene_id}", response_model=HotspotExportResponse)
@track_api_performance("/game/hotspots/export")
async def export_hotspots_template(
    city_id: str,
    scene_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    导出热点模板
    
    - 导出指定城市和场景的热点模板
    - 返回JSON格式的模板数据
    """
    template = await game_service.export_hotspots_template(city_id, scene_id)
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Template not found"
        )
    
    return HotspotExportResponse(
        city_id=city_id,
        scene_id=scene_id,
        template=template
    )


@router.get("/hotspots/templates", response_model=HotspotTemplateListResponse)
@track_api_performance("/game/hotspots/templates")
async def list_hotspots_templates(
    current_user: User = Depends(get_current_user)
):
    """
    列出所有热点模板
    
    - 返回所有可用的热点模板
    - 包括场景模板、城市模板、事件模板等
    """
    templates = await game_service.list_available_templates()
    
    return HotspotTemplateListResponse(templates=templates)


@router.delete("/hotspots/template/{city_id}/{scene_id}")
@track_api_performance("/game/hotspots/delete")
async def delete_hotspots_template(
    city_id: str,
    scene_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    删除热点模板
    
    - 删除指定城市和场景的热点模板
    - 删除后将使用默认的随机生成模式
    """
    try:
        # 获取当前模板配置
        current_templates = hotspot_templates_config.get("scene_templates", {})
        
        if scene_id in current_templates and city_id in current_templates[scene_id]:
            del current_templates[scene_id][city_id]
            
            # 如果场景下没有其他城市模板，删除场景
            if not current_templates[scene_id]:
                del current_templates[scene_id]
            
            # 更新配置
            hotspot_templates_config.set("scene_templates", current_templates)
            hotspot_templates_config.save()
            
            return {"success": True, "message": "Template deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found"
            )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete template: {str(e)}"
        )


@router.get("/scenes/{scene_id}/hotspots")
@track_api_performance("/game/scenes/hotspots")
async def get_scene_available_hotspots(
    scene_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    获取场景可用的热点名称
    
    - 返回场景中预定义的热点名称
    - 包含推荐的热点配置
    - 用于热点编辑器或模板创建
    """
    result = await game_service.get_scene_available_hotspots(scene_id)
    return result


@router.post("/scenes/{scene_id}/hotspots/validate")
@track_api_performance("/game/scenes/hotspots/validate")
async def validate_hotspot_names(
    scene_id: str,
    hotspots: List[Dict[str, Any]],
    current_user: User = Depends(get_current_user)
):
    """
    验证热点名称是否与场景配置匹配
    
    - 检查热点名称是否在场景配置中定义
    - 返回验证结果和警告信息
    - 用于热点导入前的预检查
    """
    result = await game_service.validate_hotspot_names(scene_id, hotspots)
    return result


@router.post("/user/hotspots/reset")
@track_api_performance("/game/user/hotspots/reset")
async def reset_user_scene_hotspots(
    city_id: str = "beijing",
    scene_id: str = "scene_level_1",
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    重置用户场景热点收集记录

    - 删除用户在指定场景的热点收集记录
    - 恢复到初始配置的热点状态
    - 清理相关缓存
    """
    try:
        result = await game_service.reset_user_scene_hotspots(
            db=db,
            user_id=current_user.id,
            city_id=city_id,
            scene_id=scene_id
        )

        if "error" in result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["error"]
            )

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset user hotspots: {str(e)}"
        )


@router.post("/cache/cleanup")
@track_api_performance("/game/cache/cleanup")
async def cleanup_hotspot_cache(
    current_user: User = Depends(get_current_user)
):
    """
    清理热点相关缓存

    - 清理Redis中的会话和热点缓存
    - 用于数据清理后的缓存同步
    - 管理员功能
    """
    try:
        from app.core.redis_client import redis_manager
        
        # 清理会话相关缓存
        patterns_to_clean = [
            "game_session:*",
            "hotspot:*:*",
            "boss_health:*",
            "session:*"
        ]
        
        cleaned_count = 0
        for pattern in patterns_to_clean:
            # 注意：在生产环境中应该谨慎使用keys命令
            keys = await redis_manager.redis_client.keys(pattern)
            if keys:
                await redis_manager.delete(*keys)
                cleaned_count += len(keys)
        
        return {
            "success": True,
            "message": f"Successfully cleaned {cleaned_count} cache keys",
            "patterns_cleaned": patterns_to_clean
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup cache: {str(e)}"
        )

@router.get("/hotspots/config/{scene_id}", summary="获取场景热点配置")
async def get_scene_hotspot_config(
    scene_id: str,
    current_user: str = Depends(get_current_user)
):
    """获取指定场景的热点配置详情"""
    try:
        # 从配置文件读取场景配置
        scene_hotspots_path = Path(__file__).parent.parent.parent.parent / "config" / "scene_hotspots.yaml"
        
        with open(scene_hotspots_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 获取指定场景配置
        scene_config = config_data.get("scenes", {}).get(scene_id)
        if not scene_config:
            raise HTTPException(
                status_code=404,
                detail=f"场景 {scene_id} 的热点配置不存在"
            )
        
        # 添加全局配置用于参考
        global_config = config_data.get("global_config", {})
        
        return {
            "scene_id": scene_id,
            "scene_config": scene_config,
            "global_config": global_config,
            "last_updated": datetime.utcnow()
        }
        
    except FileNotFoundError:
        raise HTTPException(
            status_code=500,
            detail="热点配置文件不存在"
        )
    except Exception as e:
        logger.error(f"获取热点配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取热点配置失败: {str(e)}"
        )

@router.post("/hotspots/config/{scene_id}/validate", summary="验证场景热点配置")
async def validate_scene_hotspot_config(
    scene_id: str,
    config_data: dict,
    current_user: str = Depends(get_current_user)
):
    """验证热点配置的合法性"""
    try:
        validation_results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        # 基础结构检查
        if "hotspot_config" not in config_data:
            validation_results["errors"].append("缺少 hotspot_config 配置")
            validation_results["valid"] = False
            
        hotspot_config = config_data.get("hotspot_config", {})
        hotspot_types = hotspot_config.get("hotspot_types", {})
        
        # 检查每种热点类型配置
        total_hotspots = 0
        for type_name, type_config in hotspot_types.items():
            if not type_config.get("enabled", False):
                continue
                
            # 检查必要字段
            if "count" not in type_config:
                validation_results["errors"].append(f"{type_name}: 缺少 count 配置")
                validation_results["valid"] = False
                
            count = type_config.get("count", 0)
            total_hotspots += count
            
            # 检查热点名称
            available_names = type_config.get("available_names", [])
            if count > 0 and not available_names:
                validation_results["errors"].append(f"{type_name}: 启用了热点但没有配置可用名称")
                validation_results["valid"] = False
            elif count > len(available_names):
                validation_results["warnings"].append(f"{type_name}: 配置数量({count})超过可用名称数量({len(available_names)})")
                
            # 检查位置配置
            position_mode = type_config.get("position_mode", "random")
            position_config = type_config.get("position_config", {})
            
            if position_mode == "predefined":
                predefined_positions = position_config.get("predefined_positions", [])
                if count > len(predefined_positions):
                    validation_results["errors"].append(f"{type_name}: 预定义位置数量不足，需要{count}个，只有{len(predefined_positions)}个")
                    validation_results["valid"] = False
                    
            elif position_mode == "random":
                random_area = position_config.get("random_area", {})
                for coord in ["x_min", "x_max", "y_min", "y_max"]:
                    if coord not in random_area:
                        validation_results["warnings"].append(f"{type_name}: 随机区域缺少 {coord} 配置")
                        
                if random_area.get("x_min", 0) >= random_area.get("x_max", 1):
                    validation_results["errors"].append(f"{type_name}: x_min 必须小于 x_max")
                    validation_results["valid"] = False
                    
                if random_area.get("y_min", 0) >= random_area.get("y_max", 1):
                    validation_results["errors"].append(f"{type_name}: y_min 必须小于 y_max")
                    validation_results["valid"] = False
            
            # 检查奖励配置
            reward_config = type_config.get("reward_config", {})
            if not reward_config:
                validation_results["warnings"].append(f"{type_name}: 缺少奖励配置")
            else:
                amount_range = reward_config.get("amount_range", {})
                if amount_range.get("min", 0) > amount_range.get("max", 0):
                    validation_results["errors"].append(f"{type_name}: 奖励最小值不能大于最大值")
                    validation_results["valid"] = False
        
        # 总数检查
        total_config = hotspot_config.get("total_hotspots", {})
        if total_config.get("enabled", False):
            min_count = total_config.get("min_count", 0)
            max_count = total_config.get("max_count", 20)
            
            if total_hotspots < min_count:
                validation_results["warnings"].append(f"当前配置总热点数({total_hotspots})少于最小要求({min_count})")
            elif total_hotspots > max_count:
                validation_results["errors"].append(f"当前配置总热点数({total_hotspots})超过最大限制({max_count})")
                validation_results["valid"] = False
        
        # 建议
        if total_hotspots == 0:
            validation_results["suggestions"].append("建议启用至少一种热点类型")
        elif total_hotspots > 15:
            validation_results["suggestions"].append("热点数量较多，可能影响游戏体验")
            
        return validation_results
        
    except Exception as e:
        logger.error(f"验证热点配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"验证热点配置失败: {str(e)}"
        )

@router.post("/hotspots/config/reload", summary="重新加载热点配置")
async def reload_hotspot_config(
    current_user: str = Depends(get_current_user)
):
    """重新加载热点配置文件（无需重启服务）"""
    try:
        # 这里可以添加配置重载逻辑
        # 比如清理相关缓存，重新读取配置文件等
        
        scene_hotspots_path = Path(__file__).parent.parent.parent.parent / "config" / "scene_hotspots.yaml"
        
        # 验证配置文件格式
        with open(scene_hotspots_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        if not config_data or "scenes" not in config_data:
            raise HTTPException(
                status_code=400,
                detail="配置文件格式错误"
            )
            
        # 这里可以添加缓存清理逻辑
        # await clear_hotspot_cache()
        
        return {
            "success": True,
            "message": "热点配置已重新加载",
            "reload_time": datetime.utcnow(),
            "scenes_count": len(config_data.get("scenes", {}))
        }
        
    except FileNotFoundError:
        raise HTTPException(
            status_code=500,
            detail="热点配置文件不存在"
        )
    except Exception as e:
        logger.error(f"重新加载热点配置失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"重新加载热点配置失败: {str(e)}"
        )

@router.get("/hotspots/config/status", summary="获取热点配置系统状态")
async def get_hotspot_config_status(
    current_user: str = Depends(get_current_user)
):
    """获取热点配置系统的状态信息"""
    try:
        scene_hotspots_path = Path(__file__).parent.parent.parent.parent / "config" / "scene_hotspots.yaml"
        
        # 检查配置文件状态
        config_file_stat = scene_hotspots_path.stat()
        
        with open(scene_hotspots_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        # 统计配置信息
        scenes = config_data.get("scenes", {})
        status_info = {
            "config_file": {
                "path": str(scene_hotspots_path),
                "exists": scene_hotspots_path.exists(),
                "size": config_file_stat.st_size,
                "last_modified": datetime.fromtimestamp(config_file_stat.st_mtime),
            },
            "scenes": {
                "total_count": len(scenes),
                "scene_list": list(scenes.keys())
            },
            "hotspot_types_summary": {},
            "system_status": {
                "hot_reload_enabled": config_data.get("admin_config", {}).get("hot_reload", False),
                "debug_mode": config_data.get("admin_config", {}).get("debug_mode", {}).get("enabled", False)
            }
        }
        
        # 统计各场景的热点类型
        for scene_id, scene_config in scenes.items():
            hotspot_types = scene_config.get("hotspot_config", {}).get("hotspot_types", {})
            enabled_types = [type_name for type_name, config in hotspot_types.items() if config.get("enabled", False)]
            total_count = sum(config.get("count", 0) for config in hotspot_types.values() if config.get("enabled", False))
            
            status_info["hotspot_types_summary"][scene_id] = {
                "enabled_types": enabled_types,
                "total_hotspots": total_count
            }
        
        return status_info

    except Exception as e:
        logger.error(f"获取热点配置状态失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"获取热点配置状态失败: {str(e)}"
        )


# 🚫 PRD合规性清理：删除弹药统计端点 - PRD中没有弹药概念


# 🚫 PRD合规性清理：删除弹药合成端点 - PRD中没有弹药概念


# 🚫 PRD合规性清理：完全删除所有弹药和大炮相关API - PRD中没有弹药和大炮概念

# 🚫 PRD合规性清理：完全删除所有弹药和大炮相关代码 - PRD中没有这些概念
# PRD核心机制：收集小偷和垃圾、BOSS战斗、体力系统、经验升级、宝箱系统、广告系统

# ===== 文件结束 =====
