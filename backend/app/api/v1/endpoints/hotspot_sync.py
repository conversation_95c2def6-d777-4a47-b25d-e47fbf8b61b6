"""
热点同步API端点
支持XML配置同步到数据库，前端动态加载热点等功能
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, text
from typing import List, Dict, Any, Optional
from pathlib import Path
import json

from app.core.database import get_db
from app.core.redis_client import redis_manager
from app.api.deps import get_current_user
from app.models.user import User
from app.models.game import SceneHotspot, UserHotspotCollection
from app.services.xml_hotspot_parser import xml_hotspot_parser
from app.schemas.game import HotspotSyncRequest, HotspotSyncResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


class HotspotLayerRequest(BaseModel):
    """热点分层加载请求"""
    city_id: str
    scene_id: str
    view_center: Dict[str, float]  # {"x": 0.0, "y": 0.0}
    view_range: Dict[str, float]   # {"width": 180.0, "height": 90.0}
    zoom_level: int = 1            # 缩放级别 1-5
    user_collected: Optional[List[str]] = None  # 用户已收集的热点


class HotspotSyncApiRequest(BaseModel):
    """XML同步请求"""
    scene_file_path: Optional[str] = None
    force_update: bool = False
    sync_all: bool = False


class LayeredHotspotsResponse(BaseModel):
    """分层热点响应"""
    success: bool
    hotspots: List[Dict[str, Any]]
    layer_info: Dict[str, Any]
    cache_key: Optional[str] = None


@router.post("/sync/xml", response_model=Dict[str, Any])
async def sync_xml_hotspots(
    request: HotspotSyncApiRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    同步XML热点配置到数据库
    
    - 支持单个文件同步或全量同步
    - 可选择是否强制更新现有数据
    - 返回详细的同步结果
    """
    try:
        if request.sync_all:
            # 同步所有场景文件
            results = await xml_hotspot_parser.scan_and_sync_all_scenes(db)
            
            success_count = sum(1 for r in results if r.get("success", False))
            total_hotspots = sum(r.get("total_hotspots", 0) for r in results if r.get("success", False))
            
            return {
                "success": True,
                "message": f"批量同步完成：{success_count}/{len(results)} 个文件成功",
                "total_scenes": len(results),
                "successful_scenes": success_count,
                "total_hotspots": total_hotspots,
                "details": results
            }
        
        elif request.scene_file_path:
            # 同步单个文件
            result = await xml_hotspot_parser.sync_xml_to_database(
                db, request.scene_file_path, request.force_update
            )
            
            # 清除相关缓存
            cache_pattern = f"hotspots:{result['city_id']}:{result['scene_id']}:*"
            await redis_manager.delete_pattern(cache_pattern)
            
            return {
                "success": True,
                "message": "XML热点同步成功",
                **result
            }
        
        else:
            # 默认同步 scene_level_1.xml
            default_file = "frontend/public/scenes/scene_level_1.xml"
            if not Path(default_file).exists():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"默认场景文件不存在: {default_file}"
                )
            
            result = await xml_hotspot_parser.sync_xml_to_database(
                db, default_file, request.force_update
            )
            
            # 清除相关缓存
            cache_pattern = f"hotspots:{result['city_id']}:{result['scene_id']}:*"
            await redis_manager.delete_pattern(cache_pattern)
            
            return {
                "success": True,
                "message": "默认场景同步成功",
                **result
            }
            
    except Exception as e:
        logger.error(f"XML热点同步失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )


@router.get("/hotspots/layered", response_model=LayeredHotspotsResponse)
async def get_layered_hotspots(
    request: HotspotLayerRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取分层热点数据（懒加载实现）
    
    - 根据用户视角和缩放级别返回相应热点
    - 支持Redis缓存提升性能
    - 过滤用户已收集的热点
    """
    try:
        city_id = request.city_id
        scene_id = request.scene_id
        zoom_level = request.zoom_level
        view_center = request.view_center
        view_range = request.view_range
        
        # 生成缓存键
        cache_key = (
            f"hotspots:{city_id}:{scene_id}:"
            f"zoom{zoom_level}:"
            f"x{view_center['x']:.1f}y{view_center['y']:.1f}:"
            f"w{view_range['width']:.1f}h{view_range['height']:.1f}"
        )
        
        # 尝试从缓存获取
        cached_data = await redis_manager.get(cache_key)
        if cached_data:
            hotspots_data = json.loads(cached_data)
            logger.info(f"从缓存获取热点数据: {cache_key}")
        else:
            # 从数据库查询热点
            hotspots_data = await _query_hotspots_by_layer(
                db, city_id, scene_id, view_center, view_range, zoom_level
            )
            
            # 缓存数据（5分钟过期）
            await redis_manager.setex(
                cache_key, 300, json.dumps(hotspots_data)
            )
            logger.info(f"缓存热点数据: {cache_key}")
        
        # 获取用户已收集的热点
        user_collected_hotspots = await _get_user_collected_hotspots(
            db, current_user.id, city_id, scene_id
        )
        
        # 过滤已收集的热点
        available_hotspots = [
            hotspot for hotspot in hotspots_data
            if hotspot["name"] not in user_collected_hotspots
        ]
        
        # 根据缩放级别调整热点密度
        filtered_hotspots = _filter_by_zoom_level(available_hotspots, zoom_level)
        
        layer_info = {
            "zoom_level": zoom_level,
            "view_center": view_center,
            "view_range": view_range,
            "total_hotspots": len(hotspots_data),
            "available_hotspots": len(available_hotspots),
            "filtered_hotspots": len(filtered_hotspots),
            "user_collected_count": len(user_collected_hotspots)
        }
        
        logger.info(f"返回分层热点: {layer_info}")
        
        return LayeredHotspotsResponse(
            success=True,
            hotspots=filtered_hotspots,
            layer_info=layer_info,
            cache_key=cache_key
        )
        
    except Exception as e:
        logger.error(f"获取分层热点失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取热点数据失败: {str(e)}"
        )


@router.get("/hotspots/database/{city_id}/{scene_id}")
async def get_database_hotspots(
    city_id: str,
    scene_id: str,
    include_collected: bool = Query(False, description="是否包含已收集的热点"),
    enabled_only: bool = Query(True, description="是否只返回启用的热点"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    从数据库获取热点配置（兼容管理后台配置）

    - 返回指定城市场景的所有热点
    - 可选择是否包含用户已收集的热点
    - 支持启用/禁用状态过滤
    - 返回详细的热点配置信息
    """
    try:
        # 查询热点数据
        query = select(SceneHotspot).where(
            and_(
                SceneHotspot.city_id == city_id,
                SceneHotspot.scene_id == scene_id
            )
        )

        # 如果只要启用的热点
        if enabled_only:
            query = query.where(SceneHotspot.enabled == True)

        result = await db.execute(query)
        hotspots = result.scalars().all()

        # 获取用户已收集的热点
        user_collected = set()
        if not include_collected:
            user_collected = await _get_user_collected_hotspots(
                db, current_user.id, city_id, scene_id
            )

        # 转换为前端格式
        hotspot_list = []
        for hotspot in hotspots:
            if not include_collected and hotspot.hotspot_name in user_collected:
                continue

            hotspot_data = {
                "id": hotspot.hotspot_name,
                "name": hotspot.hotspot_name,
                "type": hotspot.hotspot_type.value if hotspot.hotspot_type else "thief",
                "position": {
                    "x": float(hotspot.position_x),
                    "y": float(hotspot.position_y),
                    "ath": float(hotspot.position_x),  # krpano兼容
                    "atv": float(hotspot.position_y)   # krpano兼容
                },
                "scale": float(hotspot.scale) if hotspot.scale else 1.0,
                "image_url": hotspot.image_url or "",
                "visible": hotspot.visible,
                "enabled": hotspot.enabled,
                "reward": {
                    "type": hotspot.reward_type.value if hotspot.reward_type else "experience",
                    "amount": hotspot.reward_amount,
                    "data": hotspot.reward_data or {}
                },
                "onclick_action": hotspot.onclick_action or "",
                "description": hotspot.description or "",
                "created_at": hotspot.created_at.isoformat() if hotspot.created_at else None
            }
            hotspot_list.append(hotspot_data)

        return {
            "success": True,
            "city_id": city_id,
            "scene_id": scene_id,
            "total_hotspots": len(hotspots),
            "available_hotspots": len(hotspot_list),
            "user_collected_count": len(user_collected),
            "hotspots": hotspot_list
        }

    except Exception as e:
        logger.error(f"获取数据库热点失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取热点数据失败: {str(e)}"
        )


@router.post("/hotspots/update")
async def update_hotspot_config(
    city_id: str,
    scene_id: str,
    hotspot_name: str,
    hotspot_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新单个热点配置
    
    - 支持修改热点位置、奖励、类型等
    - 自动清除相关缓存
    - 记录修改日志
    """
    try:
        # 查找现有热点
        query = select(SceneHotspot).where(
            and_(
                SceneHotspot.city_id == city_id,
                SceneHotspot.scene_id == scene_id,
                SceneHotspot.hotspot_name == hotspot_name
            )
        )
        
        result = await db.execute(query)
        hotspot = result.scalar_one_or_none()
        
        if not hotspot:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"热点不存在: {hotspot_name}"
            )
        
        # 更新热点数据
        if "position" in hotspot_data:
            hotspot.position_x = hotspot_data["position"]["x"]
            hotspot.position_y = hotspot_data["position"]["y"]
        
        if "type" in hotspot_data:
            hotspot.hotspot_type = hotspot_data["type"]
        
        if "reward" in hotspot_data:
            reward = hotspot_data["reward"]
            hotspot.reward_type = reward.get("type", hotspot.reward_type)
            hotspot.reward_amount = reward.get("amount", hotspot.reward_amount)
            hotspot.reward_data = reward.get("data", hotspot.reward_data)
        
        await db.commit()
        
        # 清除相关缓存
        cache_pattern = f"hotspots:{city_id}:{scene_id}:*"
        await redis_manager.delete_pattern(cache_pattern)
        
        logger.info(f"热点配置更新成功: {city_id}/{scene_id}/{hotspot_name}")
        
        return {
            "success": True,
            "message": "热点配置更新成功",
            "hotspot_name": hotspot_name,
            "updated_fields": list(hotspot_data.keys())
        }
        
    except Exception as e:
        await db.rollback()
        logger.error(f"更新热点配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新失败: {str(e)}"
        )


@router.get("/hotspots/krpano/{city_id}/{scene_id}")
async def get_krpano_hotspots(
    city_id: str,
    scene_id: str,
    include_collected: bool = Query(False, description="是否包含已收集的热点"),
    enabled_only: bool = Query(True, description="是否只返回启用的热点"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取krpano格式的热点配置

    - 返回适用于krpano的热点配置格式
    - 包含onclick事件和显示属性
    - 支持游戏前端直接使用
    """
    try:
        # 查询热点数据
        query = select(SceneHotspot).where(
            and_(
                SceneHotspot.city_id == city_id,
                SceneHotspot.scene_id == scene_id
            )
        )

        if enabled_only:
            query = query.where(SceneHotspot.enabled == True)

        result = await db.execute(query)
        hotspots = result.scalars().all()

        # 获取用户已收集的热点
        user_collected = set()
        if not include_collected:
            user_collected = await _get_user_collected_hotspots(
                db, current_user.id, city_id, scene_id
            )

        # 转换为krpano格式
        krpano_hotspots = []
        for hotspot in hotspots:
            if not include_collected and hotspot.hotspot_name in user_collected:
                continue

            # 生成onclick事件
            onclick_action = hotspot.onclick_action
            if not onclick_action:
                # 根据类型生成默认的onclick事件
                type_actions = {
                    "thief": f"js(onThiefClicked('{hotspot.hotspot_name}', '{hotspot.reward_type.value if hotspot.reward_type else 'experience'}', {hotspot.reward_amount or 10}));",
                    "garbage": f"js(onGarbageClicked('{hotspot.hotspot_name}', '{hotspot.reward_type.value if hotspot.reward_type else 'experience'}', {hotspot.reward_amount or 5}));",
                    "treasure": f"js(onTreasureClicked('{hotspot.hotspot_name}', '{hotspot.reward_type.value if hotspot.reward_type else 'treasure_box'}', {hotspot.reward_amount or 1}));",
                    "boss_thief": f"js(onBossThiefClicked('{hotspot.hotspot_name}', '{hotspot.reward_type.value if hotspot.reward_type else 'experience'}', {hotspot.reward_amount or 100}));"
                }
                hotspot_type = hotspot.hotspot_type.value if hotspot.hotspot_type else "thief"
                onclick_action = type_actions.get(hotspot_type, f"js(onHotspotClicked('{hotspot.hotspot_name}'));")

            krpano_hotspot = {
                "name": hotspot.hotspot_name,
                "url": hotspot.image_url or _get_default_image_url(hotspot.hotspot_type.value if hotspot.hotspot_type else "thief"),
                "ath": float(hotspot.position_x),
                "atv": float(hotspot.position_y),
                "scale": float(hotspot.scale) if hotspot.scale else 1.0,
                "onclick": onclick_action,
                "visible": str(hotspot.visible).lower(),
                "data": {
                    "type": hotspot.hotspot_type.value if hotspot.hotspot_type else "thief",
                    "reward_type": hotspot.reward_type.value if hotspot.reward_type else "experience",
                    "reward_amount": hotspot.reward_amount or 0,
                    "reward_data": hotspot.reward_data or {},
                    "description": hotspot.description or ""
                }
            }
            krpano_hotspots.append(krpano_hotspot)

        return {
            "success": True,
            "city_id": city_id,
            "scene_id": scene_id,
            "total_hotspots": len(hotspots),
            "available_hotspots": len(krpano_hotspots),
            "user_collected_count": len(user_collected),
            "hotspots": krpano_hotspots
        }

    except Exception as e:
        logger.error(f"获取krpano热点失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取krpano热点失败: {str(e)}"
        )


@router.delete("/cache/hotspots")
async def clear_hotspot_cache(
    city_id: Optional[str] = Query(None),
    scene_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user)
):
    """
    清除热点缓存

    - 支持清除指定城市场景的缓存
    - 或清除所有热点缓存
    """
    try:
        if city_id and scene_id:
            cache_pattern = f"hotspots:{city_id}:{scene_id}:*"
        elif city_id:
            cache_pattern = f"hotspots:{city_id}:*"
        else:
            cache_pattern = "hotspots:*"

        deleted_count = await redis_manager.delete_pattern(cache_pattern)

        return {
            "success": True,
            "message": f"已清除 {deleted_count} 个热点缓存",
            "pattern": cache_pattern
        }

    except Exception as e:
        logger.error(f"清除热点缓存失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除缓存失败: {str(e)}"
        )


# 辅助函数

async def _query_hotspots_by_layer(
    db: AsyncSession,
    city_id: str,
    scene_id: str,
    view_center: Dict[str, float],
    view_range: Dict[str, float],
    zoom_level: int
) -> List[Dict[str, Any]]:
    """根据视角和缩放级别查询热点"""
    
    # 计算视野范围
    center_x = view_center["x"]
    center_y = view_center["y"]
    half_width = view_range["width"] / 2
    half_height = view_range["height"] / 2
    
    # 数据库查询（考虑球面坐标的边界情况）
    query = select(SceneHotspot).where(
        and_(
            SceneHotspot.city_id == city_id,
            SceneHotspot.scene_id == scene_id,
            SceneHotspot.position_x >= center_x - half_width,
            SceneHotspot.position_x <= center_x + half_width,
            SceneHotspot.position_y >= center_y - half_height,
            SceneHotspot.position_y <= center_y + half_height
        )
    )
    
    result = await db.execute(query)
    hotspots = result.scalars().all()
    
    # 转换为前端格式
    hotspot_list = []
    for hotspot in hotspots:
        hotspot_data = {
            "id": hotspot.hotspot_name,
            "name": hotspot.hotspot_name,
            "type": hotspot.hotspot_type,
            "position": {
                "x": float(hotspot.position_x),
                "y": float(hotspot.position_y)
            },
            "reward": {
                "type": hotspot.reward_type,
                "amount": hotspot.reward_amount,
                "data": hotspot.reward_data or {}
            }
        }
        hotspot_list.append(hotspot_data)
    
    return hotspot_list


async def _get_user_collected_hotspots(
    db: AsyncSession,
    user_id: int,
    city_id: str,
    scene_id: str
) -> set:
    """获取用户已收集的热点名称集合"""
    query = select(UserHotspotCollection.hotspot_name).where(
        and_(
            UserHotspotCollection.user_id == user_id,
            UserHotspotCollection.city_id == city_id,
            UserHotspotCollection.scene_id == scene_id
        )
    )
    
    result = await db.execute(query)
    return set(row[0] for row in result.fetchall())


def _filter_by_zoom_level(hotspots: List[Dict], zoom_level: int) -> List[Dict]:
    """根据缩放级别过滤热点密度"""
    if zoom_level <= 1:
        # 最远视角，只显示重要热点
        return [h for h in hotspots if h["type"] in ["treasure", "boss_thief"]]
    elif zoom_level <= 2:
        # 中远视角，显示宝箱和部分小偷
        return [h for h in hotspots if h["type"] in ["treasure", "boss_thief", "thief"]][:10]
    elif zoom_level <= 3:
        # 中等视角，显示大部分热点
        return hotspots[:15]
    else:
        # 近距离视角，显示所有热点
        return hotspots


def _get_default_image_url(hotspot_type: str) -> str:
    """根据热点类型获取默认图片URL"""
    default_images = {
        "thief": "/img/thief/thief_1_320/thief1_1.png",
        "garbage": "/img/icons/laji_icon.png",
        "treasure": "/img/treasure_box.png",
        "boss_thief": "/img/thief/thief_1_320/thief1_5.png"
    }
    return default_images.get(hotspot_type, "/img/thief/thief_1_320/thief1_1.png")