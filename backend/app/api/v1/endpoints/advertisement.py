"""
广告系统API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.services.advertisement_service import advertisement_service
from app.services.daily_task_service import daily_task_service
from app.schemas.advertisement import (
    AdWatchRequest, AdWatchResponse, AdStatusResponse, UserAdStatusResponse,
    DoubleRewardRequest, DoubleRewardResponse, TaskRewardRequest, TaskRewardResponse,
    LoginRewardResponse, LoginRewardClaimRequest, LoginRewardClaimResponse
)
from app.analytics.metrics import track_api_performance

router = APIRouter()


@router.post("/watch", response_model=AdWatchResponse)
@track_api_performance("/ads/watch")
async def watch_ad(
    request: AdWatchRequest,
    http_request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    观看广告
    
    - 记录广告观看行为
    - 验证每日观看限制
    - 为后续奖励做准备
    """
    # 获取客户端信息
    ip_address = http_request.client.host if http_request.client else "unknown"
    user_agent = http_request.headers.get("user-agent", "")
    
    result = await advertisement_service.watch_ad(
        db, current_user, request.ad_type, request.task_id, 
        request.watch_duration, request.ad_provider
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return AdWatchResponse(
        success=result["success"],
        ad_id=result.get("ad_id"),
        message=result["message"]
    )


@router.get("/status", response_model=UserAdStatusResponse)
@track_api_performance("/ads/status")
async def get_ad_status(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户广告观看状态
    
    - 显示各类型广告的每日限制
    - 显示已观看次数和剩余次数
    - 显示最后观看时间
    """
    result = await advertisement_service.get_ad_status(db, current_user)
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.post("/reward/double", response_model=DoubleRewardResponse)
@track_api_performance("/ads/reward/double")
async def apply_double_reward(
    request: DoubleRewardRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    应用双倍奖励
    
    - 验证是否观看了相应广告
    - 计算并发放双倍奖励
    - 更新用户资源
    """
    result = await advertisement_service.apply_double_reward(
        db, current_user, request.reward_type, 
        request.task_id, request.original_amount
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.post("/task/reward", response_model=TaskRewardResponse)
@track_api_performance("/ads/task/reward")
async def claim_task_reward_with_ad(
    request: TaskRewardRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    领取任务奖励（支持广告双倍）
    
    - 领取任务基础奖励
    - 可选择观看广告获得双倍奖励
    - 更新任务状态和用户资源
    """
    result = await daily_task_service.claim_task_reward(
        current_user.id, request.task_id, request.use_double_reward
    )
    
    if "error" in result:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result["error"]
        )
    
    return result


@router.get("/login/reward", response_model=LoginRewardResponse)
@track_api_performance("/ads/login/reward")
async def get_login_reward(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取登录奖励信息
    
    - 显示今日登录奖励
    - 显示连续登录天数
    - 显示是否可以领取和双倍领取
    - 预览明日奖励
    """
    # TODO: 使用登录奖励服务替换此临时实现
    # from app.services.task_service import TaskService
    # enhanced_task_service = TaskService()
    
    # 临时实现，返回基础登录奖励信息
    from datetime import datetime
    current_date = datetime.utcnow().strftime("%Y-%m-%d")
    
    return LoginRewardResponse(
        date=current_date,
        consecutive_days=1,
        reward_type="experience",
        reward_amount=100,
        is_claimed=False,
        can_claim=True,
        can_double=True, # 假设可以双倍
        next_reward_preview={
            "day": 2,
            "type": "experience",
            "amount": 150
        }
    )


@router.post("/login/claim", response_model=LoginRewardClaimResponse)
@track_api_performance("/ads/login/claim")
async def claim_login_reward(
    request: LoginRewardClaimRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    领取登录奖励
    
    - 领取每日登录奖励
    - 可选择观看广告获得双倍奖励
    - 更新连续登录天数
    """
    # TODO: 使用登录奖励服务替换此临时实现
    base_amount = 100
    bonus_amount = 0
    
    if request.use_double_reward:
        # 验证是否可以双倍
        # ad_result = await advertisement_service.verify_ad_watch(db, current_user, "login_reward")
        # if ad_result.get("success"):
        bonus_amount = base_amount
        # else:
            # raise HTTPException(status_code=400, detail="未观看广告，无法领取双倍奖励")
    
    total_amount = base_amount + bonus_amount
    
    # 发放奖励 (临时)
    current_user.exp += total_amount
    await db.commit()
    await db.refresh(current_user)
    
    return LoginRewardClaimResponse(
        success=True,
        consecutive_days=1, # 应从服务获取
        reward_type="",
        base_amount=base_amount,
        bonus_amount=bonus_amount,
        total_amount=total_amount,
        is_double_reward=request.use_double_reward,
        new_balance={
        }
    )
