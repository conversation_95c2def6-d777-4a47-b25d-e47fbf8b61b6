"""
每日任务系统API端点 - 简化版本，符合PRD要求

PRD要求的每日任务系统：
- 4种任务类型：抓捕小偷、清理垃圾、文化学习、完美通关
- 3个难度等级：简单、中等、困难
- 经验值奖励：完成任务获得经验值
- 每日重置：每天0点重置任务
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.services.daily_task_service import daily_task_service
from app.analytics.metrics import track_api_performance

router = APIRouter()


@router.get("/daily")
@track_api_performance("/task/daily")
async def get_daily_tasks(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取每日任务列表

    返回用户当天的所有任务：
    - 抓捕小偷、清理垃圾、文化学习、完美通关
    - 3个难度等级
    - 经验值奖励
    """
    result = await daily_task_service.get_user_daily_tasks(current_user.id)
    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])

    return {
        "success": True,
        "data": result
    }


@router.post("/progress/{task_type}")
@track_api_performance("/task/progress")
async def update_task_progress(
    task_type: str,
    progress_amount: int = 1,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新任务进度

    支持的任务类型：
    - catch_thief: 抓捕小偷
    - clean_rubbish: 清理垃圾
    - cultural_quiz: 文化学习
    - perfect_clear: 完美通关
    """
    from app.models.daily_task import TaskType

    # 转换任务类型
    task_type_map = {
        "catch_thief": TaskType.CATCH_THIEF,
        "clean_rubbish": TaskType.CLEAN_RUBBISH,
        "cultural_quiz": TaskType.CULTURAL_QUIZ,
        "perfect_clear": TaskType.PERFECT_CLEAR
    }

    if task_type not in task_type_map:
        raise HTTPException(status_code=400, detail="无效的任务类型")

    result = await daily_task_service.update_task_progress(
        current_user.id,
        task_type_map[task_type],
        progress_amount
    )

    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])

    return {
        "success": True,
        "data": result
    }


@router.post("/claim/{task_id}")
@track_api_performance("/task/claim")
async def claim_task_reward(
    task_id: str,
    use_ad_double: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    领取任务奖励

    - 验证任务是否完成
    - 支持观看广告双倍奖励
    - 发放经验值奖励
    """
    result = await daily_task_service.claim_task_reward(
        current_user.id,
        task_id,
        use_ad_double
    )

    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])

    return {
        "success": True,
        "data": result
    }


@router.get("/statistics")
@track_api_performance("/task/statistics")
async def get_task_statistics(
    days: int = 7,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取任务统计信息

    - 最近几天的任务完成情况
    - 任务类型分布
    - 经验值获得统计
    """
    result = await daily_task_service.get_task_statistics(current_user.id, days)

    if "error" in result:
        raise HTTPException(status_code=400, detail=result["error"])

    return {
        "success": True,
        "data": result
    }


# 任务系统API已简化完成
# 只保留PRD要求的4个基本功能：
# 1. 获取每日任务列表 (/daily)
# 2. 更新任务进度 (/progress/{task_type})
# 3. 领取任务奖励 (/claim/{task_id})
# 4. 查看任务统计 (/statistics)
