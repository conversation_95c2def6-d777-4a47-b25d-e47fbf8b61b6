"""
被动收益数据模型
"""
from datetime import datetime
from sqlalchemy import Column, Integer, BigInteger, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base


class PassiveIncomeRecord(Base):
    """被动收益记录表"""
    __tablename__ = "passive_income_records"

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    
    # 收益信息
    offline_hours = Column(Float, nullable=False, comment="离线时长(小时)")
    experience_earned = Column(Integer, nullable=False, default=0, comment="获得经验值")

    # 🚫 PRD合规性清理：移除货币相关字段 - PRD中没有货币概念
    # PRD中被动收益主要是经验值
    base_experience_per_hour = Column(Integer, nullable=False, default=50, comment="基础经验每小时")
    guardian_level = Column(Integer, nullable=False, default=0, comment="守护等级")
    bonus_multiplier = Column(Float, nullable=False, default=1.0, comment="守护等级加成倍数")
    ad_multiplier = Column(Float, nullable=False, default=1.0, comment="广告加成倍数")
    watched_ad = Column(Boolean, nullable=False, default=False, comment="是否观看广告")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    
    # 关联关系
    user = relationship("User", back_populates="passive_income_records")


class GuardianLevel(Base):
    """守护等级表"""
    __tablename__ = "guardian_levels"

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, unique=True, index=True, comment="用户ID")
    
    # 守护等级信息
    level = Column(Integer, nullable=False, default=0, comment="守护等级")
    experience = Column(Integer, nullable=False, default=0, comment="当前经验值")
    total_experience = Column(Integer, nullable=False, default=0, comment="总经验值")
    
    # 图鉴收集统计
    bronze_artifacts = Column(Integer, nullable=False, default=0, comment="青铜图鉴数量")
    silver_artifacts = Column(Integer, nullable=False, default=0, comment="白银图鉴数量")
    gold_artifacts = Column(Integer, nullable=False, default=0, comment="黄金图鉴数量")
    total_artifacts = Column(Integer, nullable=False, default=0, comment="总图鉴数量")

    # 🚫 PRD合规性清理：移除升级花费统计 - PRD中没有货币概念
    # PRD中守护者等级通过经验值自动提升，无需货币购买
    
    # 解锁的特殊能力
    unlocked_abilities = Column(Text, comment="已解锁的特殊能力(JSON格式)")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    user = relationship("User", back_populates="guardian_level_rel")


class GuardianUpgradeRecord(Base):
    """守护等级升级记录表"""
    __tablename__ = "guardian_upgrade_records"

    id = Column(BigInteger, primary_key=True, index=True)
    user_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    
    # 升级信息
    from_level = Column(Integer, nullable=False, comment="升级前等级")
    to_level = Column(Integer, nullable=False, comment="升级后等级")
    experience_gained = Column(Integer, nullable=False, comment="获得经验")
    experience_source = Column(String(50), nullable=False, comment="经验来源")  # artifact_bronze, artifact_silver, artifact_gold
    
    # 🚫 PRD合规性清理：移除消耗资源字段 - PRD中守护等级通过经验值自动提升
    
    # 解锁的新能力
    new_abilities = Column(Text, comment="新解锁的能力(JSON格式)")
    
    # 时间戳
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    
    # 关联关系
    user = relationship("User", back_populates="guardian_upgrade_records")