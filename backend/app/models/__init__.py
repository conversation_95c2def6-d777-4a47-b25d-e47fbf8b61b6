"""
数据模型包
"""

# 导入所有模型以确保正确的关系映射 - PRD合规版本
from .user import User, UserLoginLog
from .game import (
    GameSession, HotspotRecord, UserHotspotCollection,
    ShareRecord, LeaderboardSnapshot,
    AIInteraction, SceneHotspot
)
from .artifact import UserArtifact, UserCityProgress, CityCollection, UserCollection
from .advertisement import AdWatchRecord, UserAdLimit
from .config import GameConfig, SystemConfig, ConfigHistory
from .hotspot_reward import HotspotRewardConfig, SessionRandomSeed, HotspotDropHistory
from .passive_income import PassiveIncomeRecord
from .boss_health import BossHealth, BossDialogue
from .treasure_box import (
    TreasureBoxConfig, UserTreasureBox, TreasureBoxDropLog,
    BoxOpenRecord, UserBoxStatistics
)
from .daily_task import (
    DailyTaskTemplate, UserDailyTask, TaskProgressLog, TaskRewardHistory,
    DailyTaskProgress, DailyTaskExperience, DailyTaskRewardLevel
)

# 导出所有模型 - 移除非PRD合规的模型
__all__ = [
    "User", "UserLoginLog", 
    "GameSession", "HotspotRecord", "UserHotspotCollection",
    "ShareRecord", "LeaderboardSnapshot",  
    "AIInteraction", "SceneHotspot",
    "UserArtifact", "UserCityProgress", "CityCollection", "UserCollection",
    "AdWatchRecord", "UserAdLimit",  
    "GameConfig", "SystemConfig", "ConfigHistory",
    "HotspotRewardConfig", "SessionRandomSeed", "HotspotDropHistory",
    "PassiveIncomeRecord",
    "BossHealth", "BossDialogue",  # 添加PRD合规的BOSS系统
    "TreasureBoxConfig", "UserTreasureBox", "TreasureBoxDropLog",
    "BoxOpenRecord", "UserBoxStatistics",  # 添加PRD合规的宝箱系统
    "DailyTaskTemplate", "UserDailyTask", "TaskProgressLog", "TaskRewardHistory",  # 添加PRD合规的任务系统
    "DailyTaskProgress", "DailyTaskExperience", "DailyTaskRewardLevel"  # 添加缺失的任务进度模型
]