<template>
  <van-popup 
    v-model:show="visible" 
    position="center" 
    :style="{ overflow: 'visible' }"
    closeable
    :close-icon="'/img/page_icons/close-btn.png'"
    close-icon-position="top-right"
    round
  >
    <div class="task-center">
      <div class="task-title">Daily Quests</div>
      
      <!-- 奖励展示区 -->
      <div class="rewards-showcase">
        <div class="reward-item-container">

          <div class="reward-item">
            <div class="reward-icon">
              <img src="/img/page_icons/Coin.png" class="reward-star-img" alt="文明经验值" />
            </div>
            <div class="reward-value">{{ gameStore.civilizationExp }}</div>
          </div>
            <div
            v-for="(reward, index) in rewardLevels" 
              :key="index"
            class="reward-item"
            >
            <div class="reward-icon" @click="claimRewardLevel(reward)">
              <img :src="reward.icon" class="reward-icon-img" alt="reward" />
              <img v-if="reward.is_claimed" class="right-icon" src="/img/page_icons/daily_task_completed.png" alt="reward" />
              <div v-else-if="reward.can_claim" class="can-claim-indicator">!</div>
            </div>
            <div class="reward-value">{{ reward.value }}</div>
          </div>
        </div>
      </div>
        
      <!-- 刷新计时器 -->
      <div class="refresh-timer">
        Refresh timer: {{ refreshTimeFormatted }}
      </div>

      <!-- 任务列表 -->
      <div class="tasks-list">
        <!-- 加载状态 -->
        <div v-if="taskStore.loading" class="loading-state">
          <van-loading size="24px" color="#FFD700">Loading tasks...</van-loading>
        </div>

        <!-- 真实任务数据 -->
        <div
          v-for="task in taskStore.dailyTasks"
          :key="task.task_id"
          class="task-item"
          :class="{
            'completed': task.is_completed && task.is_claimed,
            'claimable': task.is_completed && !task.is_claimed,
            'in-progress': !task.is_completed
          }"
        >
          <div class="task-star">
            <img src="/img/page_icons/START-iocn.png" alt="star" />
            <span>{{ getTaskExpReward(task) }}</span>
          </div>

          <div class="task-content">
            <div class="task-name">{{ task.name || task.description }}</div>
            <div class="task-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: getProgressPercentage(task) + '%' }"
                ></div>
                <div class="progress-text">{{ task.current_progress || 0 }}/{{ task.target }}</div>
              </div>
            </div>
          </div>

          <!-- 任务状态和操作 -->
          <div v-if="task.is_completed && task.is_claimed" class="task-status">
            <img src="/img/page_icons/daily_task_completed.png" alt="completed" />
          </div>

          <div v-else-if="task.is_completed && !task.is_claimed" class="task-actions">
            <button
              class="get-reward-btn"
              @click="claimTask(task)"
              :disabled="claimingTask === task.task_id"
            >
            </button>
            <button
              class="double-reward-btn"
              @click="claimReward(task, true)"
            >
            </button>
          </div>

          <div v-else class="task-actions">
            <button class="go-now-btn" @click="goToTask(task)">
              Go Now
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!taskStore.loading && taskStore.dailyTasks.length === 0" class="empty-state">
          <p>暂无任务数据</p>
        </div>
      </div>
    </div>

  </van-popup>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useGameStore } from '@/stores/game'
import { useTaskStore } from '@/stores/task'
import { showToast, showSuccessToast, showConfirmDialog, showLoadingToast, showFailToast } from 'vant'
import { generateDailyTasks, DAILY_TASK_TEMPLATES } from '@/config/dailyTasks'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'task-completed', 'reward-claimed'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const authStore = useAuthStore()
const gameStore = useGameStore()
const taskStore = useTaskStore()

// 状态
const claimingTask = ref(null)
const claimingLevel = ref(null)


// 刷新倒计时
const refreshTime = ref(24 * 60 * 60) // 24小时（秒）
const refreshTimeFormatted = computed(() => {
  const hours = Math.floor(refreshTime.value / 3600)
  const minutes = Math.floor((refreshTime.value % 3600) / 60)
  return `${hours} h ${minutes} m`
})

// 计算属性：奖励等级（基于taskStore数据或默认配置）
const rewardLevels = computed(() => {
  if (taskStore.rewardLevels && taskStore.rewardLevels.length > 0) {
    return taskStore.rewardLevels.map(level => ({
      icon: level.icon || `/img/page_icons/reward_level_${level.level}.png`,
      value: level.required_exp,
      level: level.level,
      is_unlocked: level.is_unlocked,
      is_claimed: level.is_claimed,
      can_claim: level.can_claim,
      rewards: level.rewards
    }))
  }
  
  // 默认配置
  return [
    { icon: '/img/page_icons/reward_level_1.png', value: 20, level: 1, gold: 20, is_unlocked: false, is_claimed: false },
    { icon: '/img/page_icons/reward_level_2.png', value: 40, level: 2, gold: 40, is_unlocked: false, is_claimed: false },
    { icon: '/img/page_icons/reward_level_3.png', value: 60, level: 3, gold: 60, is_unlocked: false, is_claimed: false },
    { icon: '/img/page_icons/reward_level_4.png', value: 80, level: 4, diamond: 10, is_unlocked: false, is_claimed: false },
    { icon: '/img/page_icons/reward_level_5.png', value: 100, level: 5, diamond: 30, is_unlocked: false, is_claimed: false }
  ]
})


// 加载每日任务（使用统一的TaskStore）
const loadDailyTasks = async () => {
  await taskStore.loadTaskData()
}

// 领取任务奖励
const claimTask = async (task) => {
  if (claimingTask.value) return

  claimingTask.value = task.task_id
  try {
    const success = await taskStore.claimTaskReward(task.task_id, false)
    
    if (success) {
      // 直接给游戏store添加文明经验值
      const civilizationExpReward = getTaskCivilizationExpReward(task)
      if (civilizationExpReward > 0) {
        gameStore.addCivilizationExp(civilizationExpReward)
      }
      
      // 触发事件通知父组件
      emit('reward-claimed', {
        taskId: task.task_id,
        rewards: { civilizationExp: civilizationExpReward },
        expGained: civilizationExpReward
      })
    }
  } catch (error) {
    console.error('任务奖励领取异常:', error)
    showFailToast('奖励领取失败')
  } finally {
    claimingTask.value = null
  }
}

// 前往任务
const goToTask = (task) => {
  showToast(`前往完成任务: ${task.name}`)
  visible.value = false
}

// 领取双倍奖励
const claimDoubleReward = async (task, isDouble) => {
  try {
    showLoadingToast('观看广告中...')

    // 模拟广告观看
    setTimeout(async () => {
      try {
        const success = await taskStore.claimTaskReward(task.task_id, true)
        if (success) {
          // 给予双倍文明经验值奖励
          const civilizationExpReward = getTaskCivilizationExpReward(task) * 2
          gameStore.addCivilizationExp(civilizationExpReward)
          
          emit('reward-claimed', {
            taskId: task.task_id,
            rewards: { civilizationExp: civilizationExpReward },
            isDouble: true
          })
        }
      } catch (error) {
        showFailToast('领取双倍奖励失败')
      }
    }, 2000)
  } catch (error) {
    console.error('观看广告失败:', error)
    showFailToast('观看广告失败')
  }
}

// 工具方法
const getTaskExpReward = (task) => {
  return taskStore.getTaskExpReward(task)
}

const getTaskCivilizationExpReward = (task) => {
  // 根据任务类型给予不同的文明经验值奖励
  const taskTypeRewards = {
    'thieves': 20,    // 抓小偷任务
    'garbage': 15,    // 清垃圾任务
    'artifacts': 50,  // 收集图鉴任务
    'monuments': 30   // 保护古迹任务
  }
  
  // 检查任务描述或ID来确定任务类型
  const taskName = (task.name || task.description || '').toLowerCase()
  
  if (taskName.includes('thief') || taskName.includes('小偷')) {
    return taskTypeRewards.thieves
  } else if (taskName.includes('garbage') || taskName.includes('垃圾')) {
    return taskTypeRewards.garbage
  } else if (taskName.includes('artifact') || taskName.includes('图鉴')) {
    return taskTypeRewards.artifacts
  } else if (taskName.includes('monument') || taskName.includes('古迹')) {
    return taskTypeRewards.monuments
  }
  
  // 默认奖励
  return 25
}

const getProgressPercentage = (task) => {
  return taskStore.getProgressPercentage(task)
}

const getRewardText = (rewards) => {
  const parts = []
  if (rewards?.gold) parts.push(`${rewards.gold} 金币`)
  if (rewards?.diamond) parts.push(`${rewards.diamond} 钻石`)
  return parts.join(', ')
}

const getDoubleReward = (rewards) => {
  const doubled = {}
  if (rewards?.gold) doubled.gold = rewards.gold * 2
  if (rewards?.diamond) doubled.diamond = rewards.diamond * 2
  return doubled
}

// 领取奖励等级奖励
const claimRewardLevel = async (reward) => {
  if (!reward.can_claim) {
    if (reward.is_claimed) {
      showToast('奖励已领取')
    } else {
      showToast('奖励尚未解锁')
    }
    return
  }

  if (claimingLevel.value) return

  claimingLevel.value = reward.level
  try {
    const success = await taskStore.claimRewardLevel(reward.level)
    
    if (success) {
      console.log('奖励等级领取成功:', reward.level)
    }
  } catch (error) {
    console.error('领取奖励等级失败:', error)
    showFailToast('领取奖励失败')
  } finally {
    claimingLevel.value = null
  }
}





// 监听弹窗显示状态
watch(visible, (newVisible) => {
  if (newVisible) {
    loadDailyTasks()
  }
})

// 组件挂载时初始化
onMounted(() => {
  if (visible.value) {
    loadDailyTasks()
  }

  // 模拟倒计时
  const timer = setInterval(() => {
    refreshTime.value = Math.max(0, refreshTime.value - 1)
    if (refreshTime.value === 0) {
      clearInterval(timer)
      // 可以在这里添加刷新任务的逻辑
    }
  }, 1000)
})
</script>

<style lang="scss" scoped>
.task-center {
  width: 682px;
  // height: 1198px;
  padding: 20px 10px;
  background: url('/img/page_icons/daily_tasks_bg.png') no-repeat center center;
  background-size: 100% 100%;
  color: white;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

:deep(.van-popup__close-icon--top-right){
  top: -24px!important;
  right: -26px!important;
  .van-icon__image{
      width: 66px;
      height: 70px;

    }
  
}
.task-title {
  width:616px;
  height: 168px;
  margin: 0 auto;
  margin-top: -50px;
  font-size: 32px;
  background: url('/img/page_icons/daily_quests_title_bg.png') no-repeat center center;
  background-size: 100% 100%;
  font-weight: bold;
  color: #fff;
  display: flex;
  // align-items: center;
  justify-content: center;
  padding-top: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  // z-index: 10;
}

.rewards-showcase {
  width: 664px;
  height: 130px;
  background: url('/img/page_icons/daily_task_rewards_bg.png') no-repeat center center;
  background-size: 100% 100%;
  .reward-item-container{
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 664px;
    height: 130px;
    overflow-x: scroll;
  }
}

.reward-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.reward-icon {
  position: relative;
  width: 68px;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .reward-star-img{
    width: 30px;
    height: 30px;
  }
  .reward-icon-img {
    width: 68px;
    height: 68px;
  }
  .right-icon{
    position: absolute;
    right: -10px;
    top: -10px;
    width: 20px;
    height: 20px;
  }
  
  .can-claim-indicator {
    position: absolute;
    right: -5px;
    top: -5px;
    width: 20px;
    height: 20px;
    background: #ff4444;
    border-radius: 50%;
    color: white;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 1s infinite;
  }
}

.reward-value {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.refresh-timer {
  width: 100%;
  text-align: center;
  padding: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  margin: 10px 0;
}

.tasks-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  // padding: 0 5px;
  overflow-y: auto;
}

.task-item {
  width: 664px;
  height: 104px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  background: url('/img/page_icons/daily_task_item_bg.png') no-repeat center center;
  background-size: 100% 100%;
  gap: 10px;
  &.completed {
    opacity: 0.9;
  }
  
  // &.claimable {
  //   border: 2px solid #4CAF50;
  //   box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
  // }
}

.task-star {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  img {
    width: 30px;
    height: 30px;
  }
  
  span {
    font-size: 16px;
    font-weight: bold;
    margin-top: 2px;
  }
}

.task-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.task-name {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  position: relative;
  flex: 1;
  height: 26px;
  background: rgb(115, 115, 115);
  border-radius: 4px;
  // overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgb(117, 247, 117), rgb(79, 234,79));
  border-radius: 4px;
}

.progress-text {
  z-index: 10;
  position: absolute;
  right: 50%;
  top: 0;
  font-size: 22px;
  color: #fff;
  min-width: 45px;
  text-align: center;
}

.task-status {
  margin-left: 10px;
  width: 150px;
  text-align: center;
  img {
    width: 30px;
    height: 30px;
  }
}

.task-actions {
  display: flex;
  gap: 5px;
  margin-left: 10px;
  width: 150px;
}

.get-reward-btn {
  background: url('/img/page_icons/reward_get_btn.png') no-repeat center center;
  background-size: 100% 100%;
  width: 86px;
  height: 58px;
  border: none;
  color: white;
  font-weight: bold;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    transform: scale(1.05);
  }
}

.double-reward-btn {
  cursor: pointer;
  width:76px;
  height: 64px;
  padding: 0;
  margin-top: -4px;
  background: url('/img/page_icons/get_double_reward.png') no-repeat center center;
  background-size: 100% 100%;
  border: none;
  &:hover {
    transform: scale(1.05);
  }
}

.go-now-btn {
  background: url('/img/page_icons/go_daily_task_btn.png') no-repeat center center;
  background-size: 100% 100%;
  width: 164px;
  height: 54px;
  border: 2px solid #000;
  border-radius: 10px;
  color: white;
  text-shadow: 2px 2px 4px #3B3B3B;
  font-weight: bold;
  font-size: 22px;
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
  }
}


@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style> 