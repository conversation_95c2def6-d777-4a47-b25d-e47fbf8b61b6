<template>
  <div class="crazygames-auth">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading type="spinner" size="24px" />
      <span class="loading-text">{{ loadingText }}</span>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-icon name="warning-o" size="24px" color="#ff4444" />
      <span class="error-text">{{ error }}</span>
      <van-button 
        type="primary" 
        size="small" 
        @click="retryAuth"
        class="retry-btn"
      >
        重试
      </van-button>
    </div>
    
    <!-- 成功状态 -->
    <div v-else-if="authenticated" class="success-container">
      <van-icon name="success" size="24px" color="#07c160" />
      <span class="success-text">CrazyGames认证成功</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useAuthStore } from '@/stores/auth'
import { useGameStore } from '@/stores/game'
import { api } from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()
const gameStore = useGameStore()

// 响应式状态
const loading = ref(false)
const loadingText = ref('初始化CrazyGames...')
const error = ref('')
const authenticated = ref(false)

// 事件定义
const emit = defineEmits(['success', 'error'])

/**
 * 获取设备信息
 */
function getDeviceInfo() {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    screen: `${screen.width}x${screen.height}`,
    timestamp: new Date().toISOString()
  }
}

/**
 * 生成设备ID
 */
function generateDeviceId() {
  // 尝试从localStorage获取已存在的设备ID
  let deviceId = localStorage.getItem('device_id')
  
  if (!deviceId) {
    // 生成新的设备ID
    deviceId = 'crazygames_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    localStorage.setItem('device_id', deviceId)
  }
  
  return deviceId
}

/**
 * 获取CrazyGames用户token
 */
async function getCrazyGamesToken() {
  try {
    if (!window.CrazyGames || !window.CrazyGames.SDK) {
      throw new Error('CrazyGames SDK未加载')
    }

    loadingText.value = '获取用户token...'
    
    const token = await window.CrazyGames.SDK.user.getUserToken()
    // const token = ''
    console.log("CrazyGames token获取结果:", token ? "成功" : "失败")
    
    if (!token) {
      authStore.clearStorage()
      throw new Error('无法获取用户token')
    }

    return token
  } catch (error) {
    console.error('获取CrazyGames token失败:', error)
    throw error
  }
}

/**
 * 使用token登录后端
 */
async function loginWithToken(token) {
  try {
    loadingText.value = '验证用户身份...'
    
    const deviceId = generateDeviceId()
    const deviceInfo = getDeviceInfo()
    
    // 调用后端API
    const response = await api.auth.crazyGamesLogin(token, deviceId, deviceInfo)

    if (response.data) {
      const { user_id, token: accessToken, refresh_token, user_info } = response.data
      
      // 更新认证状态
      authStore.login({
        provider: 'crazygames',
        user: {
          user_id: user_id,
          nickname: user_info.nickname,
          avatar_url: user_info.avatar_url,
          level: user_info.level,
          exp: user_info.exp,
          vip_level: user_info.vip_level,
          total_play_time: user_info.total_play_time,
          created_at: user_info.created_at
        },
        token: accessToken,
        refreshToken: refresh_token
      })

      console.log('CrazyGames登录成功:', user_info.nickname)
      return user_info
    }
  } catch (error) {
    console.error('CrazyGames登录失败:', error)
    throw error
  }
}

/**
 * 完整的CrazyGames认证流程
 */
async function authenticateWithCrazyGames() {
  try {
    loading.value = true
    error.value = ''
    
    // 1. 获取CrazyGames token
    console.log('开始获取CrazyGames token...')
    const token = await getCrazyGamesToken()
    
    // 2. 使用token登录后端
    console.log('使用token登录后端...')
    const userInfo = await loginWithToken(token)
    
    // 3. 认证成功
    authenticated.value = true
    loadingText.value = '认证完成'
    
    // 显示成功提示
    showToast({
      message: `欢迎，${userInfo.nickname}！`,
      type: 'success',
      duration: 2000
    })
    
    // 触发成功事件
    emit('success', userInfo)
    
    // 延迟跳转到游戏页面
    setTimeout(() => {
      router.push('/game')
    }, 1000)
    
  } catch (err) {
    console.error('CrazyGames认证失败:', err)
    error.value = err.message || 'CrazyGames认证失败'

    // 启用游客模式作为备选方案
    await handleAuthenticationFailure(err)

  } finally {
    loading.value = false
  }
}

/**
 * 处理认证失败，启用游客模式
 */
async function handleAuthenticationFailure(error) {
  try {
    console.log('CrazyGames认证失败，启用游客模式')

    // 初始化游客模式
    const gameStore = useGameStore()
    gameStore.initGuestMode()

    // 显示游客模式提示
    showToast({
      message: '认证失败，以游客模式继续游戏',
      type: 'warning',
      duration: 3000
    })

    // 触发成功事件（游客模式也算成功）
    emit('success', { guestMode: true })

    // 延迟跳转到游戏页面
    setTimeout(() => {
      router.push('/game')
    }, 1000)

  } catch (guestError) {
    console.error('启用游客模式失败:', guestError)

    // 触发错误事件
    emit('error', error)

    // 显示错误提示
    showToast({
      message: '启动游戏失败，请刷新页面重试',
      type: 'fail',
      duration: 3000
    })
  }
}

/**
 * 重试认证
 */
async function retryAuth() {
  await authenticateWithCrazyGames()
}

/**
 * 检查CrazyGames SDK是否可用
 */
function checkCrazyGamesSDK() {
  return new Promise((resolve) => {
    // 检查SDK是否已经加载
    if (window.CrazyGames && window.CrazyGames.SDK) {
      resolve(true)
      return
    }
    
    // 等待SDK加载
    let attempts = 0
    const maxAttempts = 50 // 5秒超时
    
    const checkInterval = setInterval(() => {
      attempts++
      
      if (window.CrazyGames && window.CrazyGames.SDK) {
        clearInterval(checkInterval)
        resolve(true)
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval)
        resolve(false)
      }
    }, 100)
  })
}

/**
 * 初始化CrazyGames认证
 */
async function initCrazyGamesAuth() {
  try {
    loadingText.value = '检查CrazyGames环境...'
    
    // 检查是否在CrazyGames环境中
    const isInCrazyGames = window.location.hostname.includes('crazygames.com') || 
                          window.location.hostname.includes('localhost') || 
                          window.location.hostname.includes('127.0.0.1')
    
    if (!isInCrazyGames) {
      console.log('不在CrazyGames环境中，跳过CrazyGames认证')
      emit('error', new Error('不在CrazyGames环境中'))
      return
    }
    
    // 等待SDK加载
    loadingText.value = '等待CrazyGames SDK...'
    const sdkAvailable = await checkCrazyGamesSDK()
    
    if (!sdkAvailable) {
      throw new Error('CrazyGames SDK加载超时')
    }
    await window.CrazyGames.SDK.init();
    // 开始认证流程
    await authenticateWithCrazyGames()
    
  } catch (err) {
    console.error('初始化CrazyGames认证失败:', err)
    error.value = err.message || '初始化失败'
    emit('error', err)
  }
}

// 组件挂载时自动开始认证
onMounted(async () => {
  // 使用nextTick确保DOM完全渲染
  await nextTick()
  await initCrazyGamesAuth()
})

// 暴露方法供父组件调用
defineExpose({
  retryAuth,
  authenticateWithCrazyGames
})
</script>

<style scoped>
.crazygames-auth {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 100px;
}

.loading-container,
.error-container,
.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}

.loading-text,
.error-text,
.success-text {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.error-text {
  color: #ff4444;
}

.success-text {
  color: #07c160;
}

.retry-btn {
  margin-top: 8px;
  min-width: 80px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .crazygames-auth {
    padding: 16px;
  }
  
  .loading-text,
  .error-text,
  .success-text {
    font-size: 13px;
  }
}
</style>
