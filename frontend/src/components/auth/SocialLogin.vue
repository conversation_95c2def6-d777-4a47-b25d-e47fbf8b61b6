<template>
  <div v-if="inline" class="social-login-container">
    <slot name="header">
      <span class="title">{{ $t('home.quickLogin') }}</span>
      <!-- <p class="desc">{{ $t('home.subtitle') }}</p> -->
    </slot>
    <div class="login-buttons">
      <!-- <van-button type="primary" block id="g_id_onload"
            data-client_id="303981811555-kgc17gbtqvtbh8pc9hd1nl97nd63nq03.apps.googleusercontent.com"
            data-callback="handleCredentialResponse"
            data-auto_prompt="false">
        <i class="logo">🟢</i> Google
      </van-button> -->

      <!-- Google 登录按钮将渲染到这个 div 中 -->
      <div ref="googleButton" class="flex justify-center"></div>
      <van-button type="primary" block class="login-btn facebook" @click="loginWithFacebook" style="background:#1877F2; border:none;">
        <i class="logo">📘</i> Facebook
      </van-button>
      <van-button type="primary" block class="login-btn wechat" @click="loginWithWeChat" style="background:#07C160; border:none;">
        <i class="logo">💬</i> WeChat
      </van-button>
    </div>
    <div class="note">{{ tip }}</div>
  </div>

</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { api } from '@/utils/api'
import { showToast, showFailToast, showLoadingToast, closeToast } from 'vant'

const props = defineProps({
  modelValue: Boolean,
  inline: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'success'])

const show = ref(props.modelValue)
watch(() => props.modelValue, val => show.value = val)
watch(show, val => emit('update:modelValue', val))

const tip = ref('')
const authStore = useAuthStore()

const GOOGLE_CLIENT_ID = "303981811555-kgc17gbtqvtbh8pc9hd1nl97nd63nq03.apps.googleusercontent.com";

// 使用 ref 来获取 DOM 元素的引用
const googleButton = ref(null); 

// 使用 ref 来管理用户的登录状态和数据
const isLoggedIn = ref(false);
const userData = ref(null);

/**
 * 获取设备信息
 */
function getDeviceInfo() {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    screen: `${screen.width}x${screen.height}`,
    timestamp: new Date().toISOString()
  }
}

/**
 * 生成设备ID
 */
function getDeviceId() {
  let deviceId = localStorage.getItem('device_id')
  if (!deviceId) {
    deviceId = 'web_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    localStorage.setItem('device_id', deviceId)
  }
  return deviceId
}

/**
 * 这是 Google 登录成功后的回调函数
 * @param {object} response - The response object from Google.
 */
async function handleCredentialResponse(response) {
  
  try {
    showLoadingToast({
      message: '正在登录...',
      forbidClick: true,
      duration: 0
    })

    // 获取设备信息
    const deviceId = getDeviceId()
    const deviceInfo = getDeviceInfo()

    // 调用后端统一登录API进行谷歌登录
    const apiResponse = await api.auth.unifiedLogin('google', {
      googleToken: response.credential,
      deviceId: deviceId,
      deviceInfo: deviceInfo
    })

    closeToast()

    if (apiResponse.data) {
      const { user_id, token, refresh_token, user_info, google_info } = apiResponse.data
      
      // 更新认证状态
      authStore.login({
        provider: 'google',
        user: {
          user_id: user_id,
          nickname: user_info.nickname,
          avatar_url: user_info.avatar_url,
          level: user_info.level,
          exp: user_info.exp,
          vip_level: user_info.vip_level,
          total_play_time: user_info.total_play_time,
          created_at: user_info.created_at,
          email: google_info?.email
        },
        token: token,
        refreshToken: refresh_token
      })

      // 更新本地状态
      userData.value = google_info
      isLoggedIn.value = true

      // 显示成功提示
      showToast({
        message: `欢迎回来，${user_info.nickname}！`,
        type: 'success'
      })

      // 触发成功事件
      emit('success')
      show.value = false
      tip.value = ''

    }
  } catch (error) {
    closeToast()

    console.error('Google登录失败:', error)

    let errorMessage = '登录失败，请重试'
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail
    }

    // 显示错误提示，并提供游客模式选项
    showToast({
      message: errorMessage + '，是否以游客模式继续？',
      type: 'fail',
      duration: 5000
    })

    // 可以考虑自动启用游客模式作为备选方案
    // 这里暂时只显示错误，让用户选择
    tip.value = ''
  }
}

/**
 * 退出登录函数
 */
function signOut() {
  // 禁用 Google 的 One Tap 提示，并重置UI
  if (window.google) {
    window.google.accounts.id.disableAutoSelect();
  }
  
  authStore.logout()
  isLoggedIn.value = false;
  userData.value = null;
  console.log("用户已退出登录。");
}

/**
 * 动态加载脚本
 */
function loadScript(src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.src = src
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  })
}

const loginWithFacebook = async () => {
  tip.value = 'Loading Facebook SDK...'
  try {
    await loadScript('https://connect.facebook.net/en_US/sdk.js')
    /* global FB */
    FB.init({
      appId: import.meta.env.VITE_FACEBOOK_APP_ID || 'FACEBOOK_APP_ID',
      cookie: true,
      xfbml: false,
      version: 'v17.0'
    })
    FB.login((response) => {
      if (response.authResponse) {
        const { accessToken, userID } = response.authResponse
        authStore.login({ provider: 'facebook', user: { id: userID }, token: accessToken })
        emit('success')
        show.value = false
        tip.value = ''
      }
    }, { scope: 'public_profile,email' })
  } catch (e) {
    console.error(e)
    showFailToast('Facebook 登录失败')
  }
}

const loginWithWeChat = () => {
  // 对于微信网页扫码，这里可跳转到微信授权页面
  tip.value = '跳转微信二维码...'
  // 模拟成功
  setTimeout(() => {
    authStore.login({ provider: 'wechat', user: { name: 'WeChatUser' }, token: 'mock-wechat-token' })
    emit('success')
    show.value = false
    tip.value = ''
  }, 1500)
}

onMounted(() => {
  // 检查 `google` 对象是否已经加载
  if (window.google) {
    initializeGoogleLogin()
  } else {
    // 如果Google库还没加载，监听加载完成事件
    window.addEventListener('load', initializeGoogleLogin)
  }
})

function initializeGoogleLogin() {
  if (window.google && googleButton.value) {
    try {
      // 1. 初始化 Google Identity Services
      window.google.accounts.id.initialize({
        client_id: GOOGLE_CLIENT_ID,
        callback: handleCredentialResponse // 直接传递函数引用
      });

      // 2. 渲染 Google 登录按钮
      window.google.accounts.id.renderButton(
        googleButton.value, // 将按钮渲染到我们用 ref 引用的 div 上
        { 
          theme: "outline", 
          size: "large",
          type: "standard",
          text: "signin_with",
          shape: "rectangular",
          logo_alignment: "left",
          width: "100%"
        }
      );
      
      
      // 可选：显示 One Tap 提示
      // window.google.accounts.id.prompt(); 
    } catch (error) {
      
    }
  }
}
</script>

<style scoped>
.social-login-container {
  /* padding: 20px; */
  text-align: center;
}
.title {
  color: #e7e0d8;
  background: rgba(0, 0, 0, 0.4);
  padding: 10px;
  border-radius: 10px;
  font-size: 16px;
  /* font-weight: bold; */
  margin-bottom: 10px;
  text-align: left;
}
.desc {
  font-size: 14px;
  color: #888;
  margin-bottom: 20px;
}
.login-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.login-btn .logo {
  margin-right: 8px;
}
.note {
  margin-top: 15px;
  font-size: 12px;
  color: #ccc;
}
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 