/**
 * 任务系统状态管理
 * 统一管理每日任务、经验值、奖励等级等状态
 */
import { defineStore } from 'pinia'
import { api } from '@/utils/api'
import { useGameStore } from './game'
import { showSuccessToast, showFailToast } from 'vant'

export const useTaskStore = defineStore('task', {
  state: () => ({
    // 任务数据
    dailyTasks: [],
    loading: false,
    lastUpdated: null,
    
    // 经验值系统
    experience: {
      current_exp: 0,
      total_exp: 100,
      progress: 0
    },
    
    // 奖励等级
    rewardLevels: [],
    
    // 用户资源（任务相关）
    userResources: {
      civilization_exp: 0,  // 文明经验值
      stamina: 100,         // 体力值
      thieves_collected: 0,
      garbage_collected: 0,
      artifacts_collected: 0,
      monuments_protected: 0
    },
    
    // 任务摘要
    summary: {
      completed_tasks: 0,
      total_tasks: 0,
      completion_rate: 0
    }
  }),

  getters: {
    // 可领取的任务
    claimableTasks: (state) => {
      return state.dailyTasks.filter(task => task.is_completed && !task.is_claimed)
    },
    
    // 进行中的任务
    activeTasks: (state) => {
      return state.dailyTasks.filter(task => !task.is_completed)
    },
    
    // 已完成的任务
    completedTasks: (state) => {
      return state.dailyTasks.filter(task => task.is_completed)
    },
    
    // 可领取的奖励等级
    claimableRewardLevels: (state) => {
      return state.rewardLevels.filter(level => level.can_claim)
    },
    
    // 经验值进度百分比
    experienceProgress: (state) => {
      return state.experience.total_exp > 0 
        ? (state.experience.current_exp / state.experience.total_exp) * 100 
        : 0
    }
  },

  actions: {
    /**
     * 加载统一的任务数据
     */
    async loadTaskData() {
      if (this.loading) return

      this.loading = true
      try {
        const response = await api.task.getDailyTasksUnified()
        
        if (response.data.error) {
          showFailToast(response.data.error)
          return false
        }

        // 更新所有任务相关状态
        this.dailyTasks = response.data.tasks || []
        this.experience = response.data.experience || this.experience
        this.rewardLevels = response.data.reward_levels || []
        this.userResources = response.data.user_resources || this.userResources
        this.summary = response.data.summary || this.summary
        this.lastUpdated = new Date()

        // 同步到游戏store
        const gameStore = useGameStore()
        gameStore.syncTaskResources(this.userResources)

        console.log('任务数据加载成功:', {
          tasks: this.dailyTasks.length,
          exp: this.experience.current_exp,
          levels: this.rewardLevels.length
        })

        return true
      } catch (error) {
        console.error('加载任务数据失败:', error)
        showFailToast('任务数据加载失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 领取任务奖励
     */
    async claimTaskReward(taskId, useDoubleReward = false) {
      try {
        const response = await api.tasks.claimReward(taskId, useDoubleReward)

        if (response.data.error) {
          showFailToast(response.data.error)
          return false
        }

        if (response.data.success) {
          // 更新任务状态
          this.updateTaskStatus(taskId, { 
            is_claimed: true,
            is_rewarded: true 
          })

          // 更新用户资源
          if (response.data.user_resources) {
            this.userResources = response.data.user_resources
            
            // 同步到游戏store
            const gameStore = useGameStore()
            gameStore.syncTaskResources(this.userResources)
          }

          // 更新经验值
          if (response.data.experience) {
            this.experience = response.data.experience
          }

          // 显示奖励信息
          const rewards = response.data.rewards || {}
          const rewardText = this.formatRewardText(rewards)
          const message = useDoubleReward 
            ? `获得双倍奖励: ${rewardText}` 
            : `获得奖励: ${rewardText}`
          
          showSuccessToast(message)

          // 重新加载数据以获取最新的奖励等级状态
          setTimeout(() => {
            this.loadTaskData()
          }, 500)

          return true
        }
      } catch (error) {
        console.error('领取任务奖励失败:', error)
        showFailToast('奖励领取失败')
        return false
      }
    },

    /**
     * 领取奖励等级奖励
     */
    async claimRewardLevel(level) {
      try {
        const response = await api.task.claimRewardLevel(level)
        
        if (response.data.error) {
          showFailToast(response.data.error)
          return false
        }

        if (response.data.success) {
          // 更新奖励等级状态
          this.updateRewardLevelStatus(level, {
            is_claimed: true,
            can_claim: false
          })

          // 更新用户资源
          if (response.data.user_resources) {
            this.userResources = response.data.user_resources
            
            // 同步到游戏store
            const gameStore = useGameStore()
            gameStore.syncTaskResources(this.userResources)
          }

          // 显示奖励信息
          const rewardText = `${response.data.reward_amount} ${response.data.reward_type === 'gold' ? '金币' : '钻石'}`
          showSuccessToast(`获得等级奖励: ${rewardText}`)

          return true
        }
      } catch (error) {
        console.error('领取奖励等级失败:', error)
        showFailToast('领取奖励失败')
        return false
      }
    },

    /**
     * 更新单个任务状态
     */
    updateTaskStatus(taskId, updates) {
      const taskIndex = this.dailyTasks.findIndex(task => task.task_id === taskId)
      if (taskIndex !== -1) {
        this.dailyTasks[taskIndex] = {
          ...this.dailyTasks[taskIndex],
          ...updates
        }
      }
    },

    /**
     * 更新奖励等级状态
     */
    updateRewardLevelStatus(level, updates) {
      const levelIndex = this.rewardLevels.findIndex(reward => reward.level === level)
      if (levelIndex !== -1) {
        this.rewardLevels[levelIndex] = {
          ...this.rewardLevels[levelIndex],
          ...updates
        }
      }
    },

    /**
     * 格式化奖励文本
     */
    formatRewardText(rewards) {
      const parts = []
      if (rewards.civilization_exp) parts.push(`${rewards.civilization_exp} 文明经验值`)
      if (rewards.stamina) parts.push(`${rewards.stamina} 体力值`)
      if (rewards.exp) parts.push(`${rewards.exp} 经验`)
      if (rewards.gold) parts.push(`${rewards.gold} 金币`)
      if (rewards.diamond) parts.push(`${rewards.diamond} 钻石`)
      return parts.join(', ') || '无奖励'
    },

    /**
     * 获取任务经验值奖励
     */
    getTaskExpReward(task) {
      return task.exp_reward || task.rewards?.exp || 20
    },
    
    /**
     * 获取任务的文明经验值奖励
     */
    getTaskCivilizationExpReward(task) {
      // 优先从任务数据中获取文明经验值奖励
      if (task.rewards?.civilization_exp) {
        return task.rewards.civilization_exp
      }
      
      // 根据任务类型给予默认奖励
      const taskName = (task.name || task.description || '').toLowerCase()
      
      if (taskName.includes('thief') || taskName.includes('小偷')) {
        return 20    // 抓小偷任务
      } else if (taskName.includes('garbage') || taskName.includes('垃圾')) {
        return 15    // 清垃圾任务
      } else if (taskName.includes('artifact') || taskName.includes('图鉴')) {
        return 50    // 收集图鉴任务
      } else if (taskName.includes('monument') || taskName.includes('古迹')) {
        return 30    // 保护古迹任务
      }
      
      // 默认奖励
      return 25
    },

    /**
     * 计算任务进度百分比
     */
    getProgressPercentage(task) {
      if (!task.target || task.target === 0) return 0
      const progress = task.current_progress || 0
      return Math.min((progress / task.target) * 100, 100)
    },

    /**
     * 重置状态（用于登出等场景）
     */
    resetState() {
      this.$reset()
    }
  }
}) 