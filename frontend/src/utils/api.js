/**
 * API 配置和HTTP客户端
 * 统一管理前端与后端的API通信
 */
import axios from 'axios'
import { useAuthStore } from '@/stores/auth'
import { showFailToast, showLoadingToast, closeToast } from 'vant'

// API基础配置
const API_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'http://localhost:8000',  // 使用9111端口
    timeout: 10000
  },
  // 生产环境
  production: {
    baseURL: 'https://api.yourdomain.com',
    timeout: 15000
  }
}

// 获取当前环境配置
const currentConfig = API_CONFIG[import.meta.env.MODE] || API_CONFIG.development

// 创建axios实例
const apiClient = axios.create({
  baseURL: currentConfig.baseURL,
  timeout: currentConfig.timeout,
  headers: {
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const authStore = useAuthStore()
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    // 显示加载提示（可选）
    if (config.showLoading !== false) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })
    }

    return config
  },
  (error) => {
    closeToast()
    console.error('请求配置错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    closeToast()
    return response
  },
  (error) => {
    closeToast()
    
    // 处理认证错误
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      showFailToast('登录已过期，请重新登录')
      // 可以在这里跳转到登录页面
      // location.reload()
      window.location.href = '/'
      return Promise.reject(error)
    } else if (error.response?.status === 403) {
      showFailToast('权限不足，无法访问该资源')
      // location.reload('/')
      window.location.href = '/'
      return Promise.reject(error)
    }
    
    // 处理其他错误
    const errorMessage = error.response?.data?.detail || error.message || '请求失败'
    
    // 避免重复显示错误提示
    if (!error.config?.hideErrorToast) {
      showFailToast(errorMessage)
    }
    
    console.error('API请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: errorMessage
    })
    
    return Promise.reject(error)
  }
)

const api = {
  // 认证相关API
  auth: {
    // 游客登录
    guestLogin: () => apiClient.post('/api/v1/auth/guest-login'),
    
    // Google登录
    googleLogin: (token) => apiClient.post('/api/v1/auth/google', { token }),
    
    // CrazyGames登录
    crazyGamesLogin: (token, deviceId, deviceInfo) => apiClient.post('/api/v1/auth/crazygames-login', { 
      token, 
      device_id: deviceId, 
      device_info: deviceInfo 
    }),
    
    // 刷新token
    refreshToken: (refreshToken) => 
      apiClient.post('/api/v1/auth/refresh', { refresh_token: refreshToken }),
    
    // 登出
    logout: () => apiClient.post('/api/v1/auth/logout')
  },
  
  // 用户相关API
  user: {
    // 获取用户信息（使用/me端点，与后端一致）
    getProfile: () => apiClient.get('/api/v1/user/me'),
    
    // 更新用户信息
    updateProfile: (data) => apiClient.put('/api/v1/user/me', data),
    
    // 获取用户统计信息
    getStats: () => apiClient.get('/api/v1/user/stats')
  },
  
  // 游戏相关API
  game: {
    // 获取城市列表
    getCities: () => apiClient.get('/api/v1/game/cities'),
    
    // 开始游戏会话
    startSession: (cityId, sceneId) =>
      apiClient.post('/api/v1/game/session/start', {
        city_id: cityId,
        scene_id: sceneId
      }),
    
    // 热点交互 - 新文明守护者系统
    interactHotspot: (hotspotId, hotspotType, action, cityId) =>
      apiClient.post('/api/v1/game/hotspot/interact', {
        hotspot_id: hotspotId,
        hotspot_type: hotspotType,
        action: action,
        city_id: cityId
      }),

    // 收集热点 - 旧接口保持兼容（已废弃，建议使用 interactHotspot）
    collectHotspot: (sessionId, hotspotName, hotspotType) =>
      apiClient.post(`/api/v1/game/session/${sessionId}/collect`, {
        hotspot_id: hotspotName,
        hotspot_name: hotspotName,
        hotspot_type: hotspotType
      }),
    
    // 结束游戏会话
    endSession: (sessionId) =>
      apiClient.post(`/api/v1/game/session/${sessionId}/end`),
    

    // 重置用户场景热点
    resetUserSceneHotspots: (cityId = 'beijing', sceneId = 'scene_level_1') =>
      apiClient.post('/api/v1/game/user/hotspots/reset', null, {
        params: { city_id: cityId, scene_id: sceneId }
      }),

    // 获取会话弹药数量（已彻底移除 - 弹药系统已删除）
  },

  // 热点同步相关API
  hotspots: {
    // 同步XML热点配置到数据库
    syncXmlHotspots: (requestData) =>
      apiClient.post('/api/v1/hotspots/sync/xml', requestData),
    
    // 获取分层热点数据（懒加载）
    getLayeredHotspots: (requestData) =>
      apiClient.post('/api/v1/hotspots/hotspots/layered', requestData),
    
    // 从数据库获取热点配置
    getDatabaseHotspots: (cityId, sceneId, includeCollected = false) =>
      apiClient.get(`/api/v1/hotspots/hotspots/database/${cityId}/${sceneId}`, {
        params: { include_collected: includeCollected }
      }),
    
    // 更新单个热点配置
    updateHotspotConfig: (cityId, sceneId, hotspotName, hotspotData) =>
      apiClient.post('/api/v1/hotspots/hotspots/update', null, {
        params: { city_id: cityId, scene_id: sceneId, hotspot_name: hotspotName },
        data: hotspotData
      }),
    
    // 清除热点缓存
    clearHotspotCache: (cityId = null, sceneId = null) =>
      apiClient.delete('/api/v1/hotspots/cache/hotspots', {
        params: { city_id: cityId, scene_id: sceneId }
      })
  },
  

  
  // 文明守护者系统API
  civilization: {
    // 获取用户文明状态
    getUserStatus: () => apiClient.get('/api/v1/user/civilization/status'),
    
    // 获取文明经验记录
    getExpLogs: (limit = 20, offset = 0, source = 'all') =>
      apiClient.get('/api/v1/user/civilization/exp-logs', {
        params: { limit, offset, source }
      }),
    
    // 守望者升级
    levelUpGuardian: () => apiClient.post('/api/v1/user/guardian/level-up')
  },

  // 古迹问答系统API
  monuments: {
    // 获取城市古迹列表
    getList: (cityId) => apiClient.get(`/api/v1/monuments/${cityId}`),
    
    // 开始古迹挑战
    startChallenge: (monumentId, cityId) =>
      apiClient.post(`/api/v1/monuments/${monumentId}/start-challenge`, {
        city_id: cityId
      }),
    
    // 提交挑战答案
    submitChallenge: (challengeId, answers, timeSpent) =>
      apiClient.post(`/api/v1/monuments/challenge/${challengeId}/submit`, {
        answers,
        time_spent_seconds: timeSpent
      }),
    
    // 领取双倍奖励
    claimDoubleReward: (challengeId, adToken, adProvider = 'unity_ads', adType = 'rewarded_video') =>
      apiClient.post(`/api/v1/monuments/challenge/${challengeId}/double-reward`, {
        ad_completion_token: adToken,
        ad_provider: adProvider,
        ad_type: adType
      })
  },
    
  // 每日任务系统API
  tasks: {
    // 获取每日任务
    getDailyTasks: (taskDate = null) =>
      apiClient.get('/api/v1//task/daily', {
        params: taskDate ? { date: taskDate } : {}
      }),
    
    // 生成每日任务
    generateDailyTasks: (taskDate = null) =>
      apiClient.post('/api/v1/tasks/generate-daily-tasks',
        taskDate ? { date: taskDate } : {}),
    
    // 更新任务进度
    updateProgress: (task_type) =>
      apiClient.post(`/api/v1//task/progress/${task_type}`),
    // 领取任务奖励
    claimReward: (taskId, useAdDouble = false) =>
      apiClient.post(`/api/v1/task/claim/${taskId}`, {
        use_ad_double: useAdDouble
      }),
    
    // 获取统计
    getWeeklyStats: () => apiClient.get('/api/v1//task/statistics'),
  },

  // 体力系统API
  stamina: {
    // 获取体力状态
    getStatus: () => apiClient.get('/api/v1/user/stamina/status'),
    
    // 消耗体力
    consume: (amount, reason, sourceId = null) =>
      apiClient.post('/api/v1/user/stamina/consume', {
        amount, reason, source_id: sourceId
      }),
    
    // 恢复体力
    recover: (recoveryType = 'ad_reward', adToken = null, amount = 20) =>
      apiClient.post('/api/v1/user/stamina/recover', {
        recovery_type: recoveryType,
        ad_completion_token: adToken,
        amount
      }),
    
    // 自然回复
    naturalRecovery: () => apiClient.post('/api/v1/user/stamina/natural-recovery'),
    
    // 获取回复信息
    getRecoveryInfo: () => apiClient.get('/api/v1/user/stamina/recovery-info')
  },

  // 新排行榜系统API
  ranking: {
    // 获取文明经验排行榜
    getCivilizationExp: (limit = 50, cityId = null) =>
      apiClient.get('/api/v1/ranking/civilization-exp', {
        params: { limit, city_id: cityId }
      }),
    
    // 获取图鉴收集排行榜
    getCollections: (limit = 50, cityId = null) =>
      apiClient.get('/api/v1/ranking/collections', {
        params: { limit, city_id: cityId }
      }),
    
    // 获取综合排行榜
    getCombined: (limit = 50, cityId = null) =>
      apiClient.get('/api/v1/ranking/combined', {
        params: { limit, city_id: cityId }
      }),
    
    // 获取玩家详情
    getPlayerDetails: (userId) =>
      apiClient.get(`/api/v1/ranking/player/${userId}/guardian-details`),
    
    // 获取我的排名
    getMyRankings: () => apiClient.get('/api/v1/ranking/my-rank'),
    
    // 获取全球统计
    getGlobalStats: () => apiClient.get('/api/v1/ranking/stats/global'),
    
    // 刷新排行榜
    refresh: (rankingType, cityId = null) =>
      apiClient.post(`/api/v1/ranking/refresh/${rankingType}`,
        cityId ? { city_id: cityId } : {})
  },
  
  // 广告系统API  
  ads: {
    // 观看广告
    watchAd: (adType) => apiClient.post('/api/v1/ads/watch', { ad_type: adType }),
    
    // 获取广告状态
    getAdStatus: () => apiClient.get('/api/v1/ads/status'),
    
    // 领取双倍奖励
    claimDoubleReward: () => apiClient.post('/api/v1/ads/double-reward')
  },
  
  // 旧排行榜API（已废弃，请使用新的 ranking API）
  leaderboard: {
    // 以下接口已废弃，请使用 ranking API
    
    // 获取文物收藏者排行榜（兼容性接口，已移除金币/战斗数据）
    getArtifactCollectors: (limit = 100, offset = 0) =>
      apiClient.get('/api/v1/ranking/artifact-collectors', {
        params: { limit, offset }
      }),

    // 获取玩家详情（兼容性接口）
    getPlayerDetails: (userId) =>
      apiClient.get(`/api/v1/ranking/player/${userId}/details`),

    // 已废弃：请使用 ranking.getCivilizationExp() 等新接口
    // getRanking: (type = 'artifact', limit = 10) => 
    //   apiClient.get('/api/v1/leaderboard/ranking', { params: { type, limit } }),
    // getUserRank: (type = 'artifact') => 
    //   apiClient.get('/api/v1/leaderboard/user-rank', { params: { type } })
  },
  
  // 转盘抽奖API
  lottery: {
    // 转盘抽奖
    spin: () => apiClient.post('/api/v1/lottery/spin'),
    
    // 获取抽奖状态
    getStatus: () => apiClient.get('/api/v1/lottery/status')
  },

  // 被动收益API
  passiveIncome: {
    // 获取被动收益状态
    getStatus: () => apiClient.get('/api/v1/passive/status'),
    
    // 领取被动收益
    claim: () => apiClient.post('/api/v1/passive/claim'),
    
    // 升级守护等级
    upgradeGuardian: () => apiClient.post('/api/v1/passive/upgrade-guardian')
  },

  // 宝箱系统API
  treasure: {
    // 获取用户宝箱列表
    getUserBoxes: (status = null, limit = 20, offset = 0) =>
      apiClient.get('/api/v1/treasure/user/boxes', {
        params: { status, limit, offset }
      }),
    
    // 开启宝箱
    openBox: (boxId, sourceAction, useAdDouble = false, context = null) =>
      apiClient.post('/api/v1/treasure/open', {
        box_id: boxId,
        source_action: sourceAction,
        use_ad_double: useAdDouble,
        context
      }),
    
    // 领取宝箱奖励
    claimReward: (boxId, useAdDouble = false) =>
      apiClient.post('/api/v1/treasure/claim', {
        box_id: boxId,
        use_ad_double: useAdDouble
      }),
    
    // 触发宝箱检查 - 抓捕小偷
    triggerThief: (count = 1, context = null) =>
      apiClient.post('/api/v1/treasure/trigger/thief', {
        count, context
      }),
    
    // 触发宝箱检查 - 清理垃圾
    triggerRubbish: (count = 1, context = null) =>
      apiClient.post('/api/v1/treasure/trigger/rubbish', {
        count, context
      }),
    
    // 触发宝箱检查 - 古迹问答
    triggerMonument: (correctAnswers = 1, context = null) =>
      apiClient.post('/api/v1/treasure/trigger/monument', {
        correct_answers: correctAnswers,
        context
      }),
    
    // 🚫 PRD合规性清理：移除BOSS击败触发API
    // PRD中没有主动攻击BOSS的机制，BOSS血量通过收集行为自动减少
    
    // 获取宝箱配置
    getConfig: () => apiClient.get('/api/v1/treasure/config'),
    
    // 获取掉落率信息
    getDropRates: () => apiClient.get('/api/v1/treasure/drop_rates'),
    
    // 获取宝箱统计
    getStatistics: (days = 7) =>
      apiClient.get('/api/v1/treasure/statistics', {
        params: { days }
      })
  },

  // 文化问答系统API
  culture: {
    // 获取问答题目
    getQuestions: (cityId, monumentId = null, difficulty = 'medium', count = 5) =>
      apiClient.get('/api/v1/culture/questions', {
        params: { city_id: cityId, monument_id: monumentId, difficulty, count }
      }),
    
    // 提交问答答案
    submitAnswers: (cityId, answers, sessionId = null, monumentId = null) =>
      apiClient.post('/api/v1/culture/submit', {
        city_id: cityId,
        answers,
        session_id: sessionId,
        monument_id: monumentId
      }),
    
    // 获取问答统计
    getStatistics: (cityId = null, days = 7) =>
      apiClient.get('/api/v1/culture/statistics', {
        params: { city_id: cityId, days }
      }),
    
    // 获取问答配置
    getConfig: (cityId = null) =>
      apiClient.get('/api/v1/culture/config', {
        params: cityId ? { city_id: cityId } : {}
      })
  },

  // 经验值系统API
  experience: {
    // 获取用户经验状态
    getStatus: () => apiClient.get('/api/v1/experience/status'),
    
    // 增加经验值
    addExperience: (amount, source, sourceId = null, metadata = null) =>
      apiClient.post('/api/v1/experience/add', {
        amount, source, source_id: sourceId, metadata
      }),
    
    // 获取经验记录
    getLogs: (limit = 20, offset = 0, source = 'all') =>
      apiClient.get('/api/v1/experience/logs', {
        params: { limit, offset, source }
      }),
    
    // 获取等级信息
    getLevelInfo: (level = null) =>
      apiClient.get('/api/v1/experience/level-info', {
        params: level ? { level } : {}
      })
  },

  // 守护者系统API
  guardian: {
    // 获取守护者状态
    getStatus: () => apiClient.get('/api/v1/guardian/status'),
    
    // 守护者升级
    levelUp: () => apiClient.post('/api/v1/guardian/level-up'),
    
    // 获取守护者配置
    getConfig: () => apiClient.get('/api/v1/guardian/config'),
    
    // 获取被动收益信息
    getPassiveIncome: () => apiClient.get('/api/v1/guardian/passive-income')
  }
}

// 同时提供命名导出和默认导出以保持兼容性
export { api }
export default api
