# VR城市游戏 - 精确DPI适配系统

本项目采用了先进的响应式适配方案，结合 amfe-flexible、postcss-pxtorem 和自定义 DPI 检测，实现了跨设备的精确显示效果。

## 🎯 技术架构

### 核心组件
- **amfe-flexible**: 基础rem适配，根据屏幕宽度动态设置根字体大小
- **postcss-pxtorem**: 构建时将px自动转换为rem
- **ResponsiveAdapter**: 自定义DPI检测和精确适配
- **responsive.scss**: DPI相关的媒体查询和样式优化

### 适配流程
1. **amfe-flexible** 设置基础rem值 (屏幕宽度/10)
2. **ResponsiveAdapter** 检测设备DPI并进行微调
3. **postcss-pxtorem** 在构建时转换px为rem
4. **CSS媒体查询** 针对不同DPI提供优化样式

## 📱 设备适配策略

### DPI分类
- **1x DPI** (普通屏幕): 标准显示，图标稍微放大
- **2x DPI** (Retina): 高清显示，字体微调，细边框优化
- **3x DPI** (超高清): 超清显示，字体缩小，图片优化

### 设备类型
- **mobile-small**: ≤480px 小屏手机
- **mobile**: ≤767px 手机
- **mobile-retina**: 手机 + 2x DPI
- **tablet**: 768px-1023px 平板
- **tablet-retina**: 平板 + 2x DPI
- **desktop**: ≥1024px 桌面
- **desktop-retina**: 桌面 + 2x DPI

## 🔧 开发指南

### 样式编写
```scss
// 直接使用px值，postcss会自动转换
.button {
  width: 100px;        // 自动转换为 1.33rem
  height: 50px;        // 自动转换为 0.67rem
  font-size: 16px;     // 自动转换为 0.21rem
}

// 使用SCSS变量
.title {
  font-size: $font-lg; // 36px -> 0.48rem
  margin: $spacing-md; // 20px -> 0.27rem
}

// 使用响应式混合器
.responsive-text {
  @include responsive-font(14px, 16px, 18px);
  // 手机14px, 平板16px, 桌面18px，高DPI自动调整
}
```

### DPI特定样式
```scss
// 针对不同DPI的样式
.icon {
  width: 24px;
  height: 24px;
  
  @include retina {
    // 2x DPI下的优化
    image-rendering: crisp-edges;
  }
  
  @include ultra-retina {
    // 3x DPI下的优化
    transform: scale(0.9);
  }
}
```

### JavaScript API
```javascript
import { getDeviceInfo, adaptPx } from '@/utils/responsive.js'

// 获取设备信息
const deviceInfo = getDeviceInfo()
console.log(deviceInfo.devicePixelRatio) // 设备像素比
console.log(deviceInfo.deviceType)       // 设备类型

// 适配尺寸计算
const remValue = adaptPx(50, 'rem')  // 50px转rem
const pxValue = adaptPx(50, 'px')    // 50px转实际px
const vwValue = adaptPx(50, 'vw')    // 50px转vw
```

## 🎨 CSS类名系统

### 自动应用的类名
HTML元素会自动获得以下类名：
```html
<!-- 示例：iPhone 13 Pro -->
<html class="dpi-3x mobile-retina high-dpi">

<!-- 示例：iPad Pro -->
<html class="dpi-2x tablet-retina high-dpi">

<!-- 示例：MacBook Pro -->
<html class="dpi-2x desktop-retina high-dpi">
```

### 工具类
```scss
.show-mobile-only    // 仅手机显示
.show-tablet-only    // 仅平板显示
.show-desktop-only   // 仅桌面显示
.show-retina-only    // 仅高DPI显示
.hide-mobile         // 手机隐藏
.hide-retina         // 高DPI隐藏
```

## 🛠️ 调试工具

### 设备信息调试面板
在开发模式下，首页右下角会显示 📱 按钮，点击可查看：
- 设备像素比
- 屏幕尺寸
- 设备类型
- 当前根字体大小
- 应用的CSS类名
- 尺寸转换对比
- 实时监控

### 调试信息
```javascript
// 在控制台中查看适配信息
console.log('ResponsiveAdapter initialized:', adaptInfo)
```

## 📐 设计规范

### 基准尺寸
- **设计稿宽度**: 750px
- **基础字体**: 16px
- **转换比例**: 1px = 1/75 rem

### 常用尺寸
```scss
// 字体大小
$font-xs: 20px;    // 超小号文字
$font-sm: 24px;    // 小号文字  
$font-base: 28px;  // 基础文字
$font-lg: 36px;    // 大号文字
$font-xl: 48px;    // 超大号文字
$font-xxl: 60px;   // 特大号文字

// 间距
$spacing-xs: 8px;
$spacing-sm: 12px;
$spacing-md: 20px;
$spacing-lg: 32px;
$spacing-xl: 48px;

// 图标
$icon-sm: 32px;
$icon-md: 48px;
$icon-lg: 64px;
```

## 🔍 常见问题

### Q: 为什么不用vw单位？
A: vw基于视口宽度，在横屏时会导致元素过大。rem方案更稳定。

### Q: 高DPI设备上字体模糊怎么办？
A: 系统会自动应用字体渲染优化，包括 antialiased 和 optimizeLegibility。

### Q: 如何处理1px边框问题？
A: 高DPI设备上会自动应用0.5px边框，通过 `.border-thin` 类名实现。

### Q: 开发时如何测试不同DPI？
A: 使用Chrome DevTools的设备模拟，或者访问调试面板查看实时信息。

## 📈 性能优化

- **CSS类名缓存**: 避免重复计算
- **防抖优化**: resize事件300ms防抖
- **方向变化**: orientationchange事件500ms延迟
- **自动清理**: 组件卸载时清理定时器

## 🔄 版本历史

### v2.0 - 精确DPI适配
- 新增 ResponsiveAdapter 系统
- 支持设备像素比检测
- 自动DPI样式优化
- 设备信息调试工具

### v1.0 - 基础适配
- amfe-flexible基础适配
- postcss-pxtorem自动转换
- SCSS变量系统

---


