"""
增强版热点管理服务
"""
import xml.etree.ElementTree as ET
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional, Any
import logging
import re

from app.models.game_data import SceneHotspot

logger = logging.getLogger(__name__)


class HotspotServiceEnhanced:
    """增强版热点管理服务"""

    async def get_hotspots_with_filters(
        self,
        db: AsyncSession,
        scene_id: Optional[str] = None,
        city_id: Optional[str] = None,
        hotspot_type: Optional[str] = None,
        visible: Optional[bool] = None,
        enabled: Optional[bool] = None,
        has_reward: Optional[bool] = None,
        reward_type: Optional[str] = None,
        search: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取热点列表（支持多维度筛选）
        """
        try:
            # 构建查询条件
            query = select(SceneHotspot)
            conditions = []

            if scene_id:
                conditions.append(SceneHotspot.scene_id == scene_id)
            if city_id:
                conditions.append(SceneHotspot.city_id == city_id)
            if hotspot_type:
                conditions.append(SceneHotspot.hotspot_type == hotspot_type)
            if visible is not None:
                conditions.append(SceneHotspot.visible == visible)
            if enabled is not None:
                conditions.append(SceneHotspot.enabled == enabled)
            if has_reward is not None:
                conditions.append(SceneHotspot.has_reward == has_reward)
            if reward_type:
                conditions.append(SceneHotspot.reward_type == reward_type)
            if search:
                conditions.append(SceneHotspot.hotspot_name.ilike(f"%{search}%"))

            if conditions:
                query = query.where(and_(*conditions))

            # 获取总数
            count_query = select(func.count()).select_from(
                query.subquery()
            )
            total_result = await db.execute(count_query)
            total = total_result.scalar()

            # 分页查询
            offset = (page - 1) * page_size
            query = query.offset(offset).limit(page_size)
            query = query.order_by(SceneHotspot.created_at.desc())

            result = await db.execute(query)
            hotspots = result.scalars().all()

            # 转换为字典
            hotspot_list = [hotspot.to_dict() for hotspot in hotspots]

            return {
                "hotspots": hotspot_list,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": (total + page_size - 1) // page_size
                }
            }

        except Exception as e:
            logger.error(f"获取热点列表失败: {e}")
            raise

    async def batch_update_hotspots(
        self,
        db: AsyncSession,
        hotspot_ids: List[int],
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        批量更新热点
        """
        try:
            # 验证热点存在
            query = select(SceneHotspot).where(SceneHotspot.id.in_(hotspot_ids))
            result = await db.execute(query)
            existing_hotspots = result.scalars().all()
            
            if len(existing_hotspots) != len(hotspot_ids):
                missing_ids = set(hotspot_ids) - {h.id for h in existing_hotspots}
                return {
                    "success": False,
                    "error": f"以下热点ID不存在: {missing_ids}"
                }

            # 执行批量更新
            update_stmt = update(SceneHotspot).where(
                SceneHotspot.id.in_(hotspot_ids)
            ).values(**update_data)
            
            await db.execute(update_stmt)
            await db.commit()

            return {
                "success": True,
                "message": f"成功更新 {len(hotspot_ids)} 个热点",
                "updated_count": len(hotspot_ids)
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"批量更新热点失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def batch_delete_hotspots(
        self,
        db: AsyncSession,
        hotspot_ids: List[int]
    ) -> Dict[str, Any]:
        """
        批量删除热点
        """
        try:
            # 验证热点存在
            query = select(func.count()).where(SceneHotspot.id.in_(hotspot_ids))
            result = await db.execute(query)
            existing_count = result.scalar()
            
            if existing_count != len(hotspot_ids):
                return {
                    "success": False,
                    "error": f"部分热点ID不存在，预期 {len(hotspot_ids)} 个，实际找到 {existing_count} 个"
                }

            # 执行批量删除
            delete_stmt = delete(SceneHotspot).where(SceneHotspot.id.in_(hotspot_ids))
            await db.execute(delete_stmt)
            await db.commit()

            return {
                "success": True,
                "message": f"成功删除 {len(hotspot_ids)} 个热点",
                "deleted_count": len(hotspot_ids)
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"批量删除热点失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_hotspot_stats(
        self,
        db: AsyncSession,
        scene_id: Optional[str] = None,
        city_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取热点统计信息
        """
        try:
            # 基础查询条件
            base_conditions = []
            if scene_id:
                base_conditions.append(SceneHotspot.scene_id == scene_id)
            if city_id:
                base_conditions.append(SceneHotspot.city_id == city_id)

            # 总体统计
            total_query = select(func.count()).select_from(SceneHotspot)
            if base_conditions:
                total_query = total_query.where(and_(*base_conditions))
            
            total_result = await db.execute(total_query)
            total_count = total_result.scalar()

            # 按类型统计
            type_stats_query = select(
                SceneHotspot.hotspot_type,
                func.count().label('count')
            ).group_by(SceneHotspot.hotspot_type)
            
            if base_conditions:
                type_stats_query = type_stats_query.where(and_(*base_conditions))
            
            type_result = await db.execute(type_stats_query)
            type_stats = {row.hotspot_type: row.count for row in type_result}

            # 可见性统计
            visibility_query = select(
                SceneHotspot.visible,
                func.count().label('count')
            ).group_by(SceneHotspot.visible)
            
            if base_conditions:
                visibility_query = visibility_query.where(and_(*base_conditions))
            
            vis_result = await db.execute(visibility_query)
            visibility_stats = {row.visible: row.count for row in vis_result}

            # 启用状态统计
            enabled_query = select(
                SceneHotspot.enabled,
                func.count().label('count')
            ).group_by(SceneHotspot.enabled)
            
            if base_conditions:
                enabled_query = enabled_query.where(and_(*base_conditions))
            
            enabled_result = await db.execute(enabled_query)
            enabled_stats = {row.enabled: row.count for row in enabled_result}

            # 奖励统计
            reward_query = select(
                SceneHotspot.has_reward,
                SceneHotspot.reward_type,
                func.count().label('count')
            ).group_by(SceneHotspot.has_reward, SceneHotspot.reward_type)
            
            if base_conditions:
                reward_query = reward_query.where(and_(*base_conditions))
            
            reward_result = await db.execute(reward_query)
            reward_stats = {}
            for row in reward_result:
                if row.has_reward:
                    reward_stats[row.reward_type] = reward_stats.get(row.reward_type, 0) + row.count
                else:
                    reward_stats['no_reward'] = reward_stats.get('no_reward', 0) + row.count

            return {
                "total_count": total_count,
                "type_stats": type_stats,
                "visibility_stats": {
                    "visible": visibility_stats.get(True, 0),
                    "hidden": visibility_stats.get(False, 0)
                },
                "enabled_stats": {
                    "enabled": enabled_stats.get(True, 0),
                    "disabled": enabled_stats.get(False, 0)
                },
                "reward_stats": reward_stats
            }

        except Exception as e:
            logger.error(f"获取热点统计失败: {e}")
            raise

    async def parse_xml_file(self, xml_content: str) -> Dict[str, Any]:
        """
        解析XML文件并提取热点信息
        """
        try:
            root = ET.fromstring(xml_content)
            hotspots = []
            
            # 查找所有hotspot元素
            for hotspot_elem in root.iter('hotspot'):
                hotspot_data = self._extract_hotspot_from_xml(hotspot_elem)
                if hotspot_data:
                    hotspots.append(hotspot_data)
            
            return {
                "success": True,
                "hotspots": hotspots,
                "count": len(hotspots)
            }

        except ET.ParseError as e:
            logger.error(f"XML解析错误: {e}")
            return {
                "success": False,
                "error": f"XML格式错误: {str(e)}"
            }
        except Exception as e:
            logger.error(f"解析XML文件失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _extract_hotspot_from_xml(self, hotspot_elem) -> Optional[Dict[str, Any]]:
        """
        从XML元素中提取热点数据
        """
        try:
            name = hotspot_elem.get('name')
            if not name:
                return None

            # 提取基础信息
            hotspot_data = {
                'hotspot_name': name,
                'position_x': float(hotspot_elem.get('ath', 0)),
                'position_y': float(hotspot_elem.get('atv', 0)),
                'scale': float(hotspot_elem.get('scale', 1.0)),
                'image_url': hotspot_elem.get('url', ''),
                'onclick_action': hotspot_elem.get('onclick', ''),
                'visible': hotspot_elem.get('visible', 'true').lower() == 'true',
                'enabled': hotspot_elem.get('enabled', 'true').lower() == 'true'
            }

            # 自动检测热点类型
            hotspot_type = self._detect_hotspot_type(name, hotspot_data['image_url'])
            hotspot_data['hotspot_type'] = hotspot_type

            # 提取奖励信息
            reward_info = self._extract_reward_info(hotspot_elem, hotspot_type)
            hotspot_data.update(reward_info)

            return hotspot_data

        except Exception as e:
            logger.warning(f"提取热点数据失败: {e}")
            return None

    def _detect_hotspot_type(self, name: str, url: str) -> str:
        """
        根据名称和URL自动检测热点类型
        """
        name_lower = name.lower()
        url_lower = url.lower()
        
        # 检测模式
        patterns = {
            'boss_thief': ['boss', 'king', '王'],
            'thief': ['thief', 'steal', '小偷', '盗贼'],
            'garbage': ['garbage', 'trash', 'laji', '垃圾', '军团'],
            'treasure': ['treasure', 'box', '宝箱', '宝藏']
        }
        
        for hotspot_type, keywords in patterns.items():
            for keyword in keywords:
                if keyword in name_lower or keyword in url_lower:
                    return hotspot_type
        
        return 'thief'  # 默认类型

    def _extract_reward_info(self, hotspot_elem, hotspot_type: str) -> Dict[str, Any]:
        """
        提取奖励信息
        """
        reward_info = {
            'has_reward': True,
            'reward_type': 'gold',  # 默认奖励类型
            'reward_min': 10,
            'reward_max': 10,
            'reward_amount': 10  # 兼容性
        }

        # 从XML属性中提取奖励信息
        if hotspot_elem.get('reward'):
            reward_amount = int(hotspot_elem.get('reward', 10))
            reward_info['reward_min'] = reward_amount
            reward_info['reward_max'] = reward_amount
            reward_info['reward_amount'] = reward_amount

        if hotspot_elem.get('reward_min') and hotspot_elem.get('reward_max'):
            reward_info['reward_min'] = int(hotspot_elem.get('reward_min'))
            reward_info['reward_max'] = int(hotspot_elem.get('reward_max'))

        if hotspot_elem.get('reward_type'):
            reward_info['reward_type'] = hotspot_elem.get('reward_type')
        else:
            # 根据热点类型设置默认奖励类型
            type_reward_map = {
                'thief': 'experience',
                'garbage': 'experience',
                'treasure': 'experience',
                'boss_thief': 'experience'
            }
            reward_info['reward_type'] = type_reward_map.get(hotspot_type, 'experience')

        # 检查是否禁用奖励
        if hotspot_elem.get('has_reward', 'true').lower() == 'false':
            reward_info['has_reward'] = False

        return reward_info

    async def save_hotspots_to_db(
        self,
        db: AsyncSession,
        scene_id: str,
        city_id: str,
        hotspots: List[Dict[str, Any]],
        created_by: str,
        incremental: bool = True
    ) -> Dict[str, Any]:
        """
        保存热点数据到数据库
        """
        try:
            saved_count = 0
            updated_count = 0

            # 如果不是增量模式，先删除现有数据
            if not incremental:
                delete_stmt = delete(SceneHotspot).where(
                    and_(
                        SceneHotspot.scene_id == scene_id,
                        SceneHotspot.city_id == city_id
                    )
                )
                await db.execute(delete_stmt)

            for hotspot_data in hotspots:
                # 设置场景和城市信息
                hotspot_data['scene_id'] = scene_id
                hotspot_data['city_id'] = city_id
                hotspot_data['created_by'] = created_by

                if incremental:
                    # 增量模式：检查是否已存在
                    existing_query = select(SceneHotspot).where(
                        and_(
                            SceneHotspot.scene_id == scene_id,
                            SceneHotspot.city_id == city_id,
                            SceneHotspot.hotspot_name == hotspot_data['hotspot_name']
                        )
                    )
                    result = await db.execute(existing_query)
                    existing = result.scalar_one_or_none()

                    if existing:
                        # 更新现有热点
                        for key, value in hotspot_data.items():
                            if key not in ['scene_id', 'city_id', 'created_by', 'created_at']:
                                setattr(existing, key, value)
                        updated_count += 1
                    else:
                        # 创建新热点
                        new_hotspot = SceneHotspot(**hotspot_data)
                        db.add(new_hotspot)
                        saved_count += 1
                else:
                    # 全量模式：直接创建
                    new_hotspot = SceneHotspot(**hotspot_data)
                    db.add(new_hotspot)
                    saved_count += 1

            await db.commit()

            return {
                "success": True,
                "message": f"成功保存 {saved_count} 个新热点，更新 {updated_count} 个现有热点",
                "saved_count": saved_count,
                "updated_count": updated_count
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"保存热点到数据库失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def duplicate_hotspots(
        self,
        db: AsyncSession,
        source_scene_id: str,
        target_scene_id: str,
        target_city_id: str,
        hotspot_ids: List[int],
        created_by: str
    ) -> Dict[str, Any]:
        """
        复制热点到其他场景
        """
        try:
            # 获取源热点
            if hotspot_ids:
                query = select(SceneHotspot).where(
                    and_(
                        SceneHotspot.scene_id == source_scene_id,
                        SceneHotspot.id.in_(hotspot_ids)
                    )
                )
            else:
                query = select(SceneHotspot).where(
                    SceneHotspot.scene_id == source_scene_id
                )

            result = await db.execute(query)
            source_hotspots = result.scalars().all()

            if not source_hotspots:
                return {
                    "success": False,
                    "error": "未找到要复制的热点"
                }

            duplicated_count = 0
            for hotspot in source_hotspots:
                # 创建新热点（排除ID和时间戳）
                new_hotspot_data = {
                    'scene_id': target_scene_id,
                    'city_id': target_city_id,
                    'hotspot_name': hotspot.hotspot_name,
                    'hotspot_type': hotspot.hotspot_type,
                    'position_x': hotspot.position_x,
                    'position_y': hotspot.position_y,
                    'scale': hotspot.scale,
                    'image_url': hotspot.image_url,
                    'visible': hotspot.visible,
                    'enabled': hotspot.enabled,
                    'has_reward': hotspot.has_reward,
                    'reward_type': hotspot.reward_type,
                    'reward_min': hotspot.reward_min,
                    'reward_max': hotspot.reward_max,
                    'reward_amount': hotspot.reward_amount,
                    'reward_data': hotspot.reward_data,
                    'onclick_action': hotspot.onclick_action,
                    'description': hotspot.description,
                    'created_by': created_by
                }

                new_hotspot = SceneHotspot(**new_hotspot_data)
                db.add(new_hotspot)
                duplicated_count += 1

            await db.commit()

            return {
                "success": True,
                "message": f"成功复制 {duplicated_count} 个热点到场景 {target_scene_id}",
                "duplicated_count": duplicated_count
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"复制热点失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def update_hotspot(
        self,
        db: AsyncSession,
        hotspot_id: int,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新单个热点
        """
        try:
            query = select(SceneHotspot).where(SceneHotspot.id == hotspot_id)
            result = await db.execute(query)
            hotspot = result.scalar_one_or_none()

            if not hotspot:
                return {
                    "success": False,
                    "error": "热点不存在"
                }

            # 更新字段
            for key, value in update_data.items():
                if hasattr(hotspot, key):
                    setattr(hotspot, key, value)

            await db.commit()
            await db.refresh(hotspot)

            return {
                "success": True,
                "message": "热点更新成功",
                "hotspot": hotspot.to_dict()
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"更新热点失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def delete_hotspot(
        self,
        db: AsyncSession,
        hotspot_id: int
    ) -> Dict[str, Any]:
        """
        删除单个热点
        """
        try:
            query = select(SceneHotspot).where(SceneHotspot.id == hotspot_id)
            result = await db.execute(query)
            hotspot = result.scalar_one_or_none()

            if not hotspot:
                return {
                    "success": False,
                    "error": "热点不存在"
                }

            await db.delete(hotspot)
            await db.commit()

            return {
                "success": True,
                "message": "热点删除成功"
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"删除热点失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }


# 创建服务实例
hotspot_service_enhanced = HotspotServiceEnhanced()