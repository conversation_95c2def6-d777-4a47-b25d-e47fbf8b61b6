"""
仪表板数据服务
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from app.models.game_data import GameUser, GameSession, UserLoginLog, BossBattle, AdWatchRecord, SessionStatus
from app.core.redis_client import redis_manager

logger = logging.getLogger(__name__)


class DashboardService:
    """仪表板数据服务"""
    
    async def get_overview_stats(self, db: AsyncSession) -> Dict:
        """获取概览统计数据"""
        try:
            # 总用户数
            total_users_stmt = select(func.count(GameUser.id)).where(GameUser.is_deleted == False)
            total_users_result = await db.execute(total_users_stmt)
            total_users = total_users_result.scalar() or 0
            
            # 今日活跃用户（今日有登录记录）
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            
            active_users_stmt = select(func.count(func.distinct(UserLoginLog.user_id))).where(
                UserLoginLog.login_time >= today_start
            )
            active_users_result = await db.execute(active_users_stmt)
            active_users = active_users_result.scalar() or 0
            
            # 总游戏会话数
            total_sessions_stmt = select(func.count(GameSession.id))
            total_sessions_result = await db.execute(total_sessions_stmt)
            total_sessions = total_sessions_result.scalar() or 0
            
            # 今日新增用户
            new_users_stmt = select(func.count(GameUser.id)).where(
                and_(
                    GameUser.created_at >= today_start,
                    GameUser.is_deleted == False
                )
            )
            new_users_result = await db.execute(new_users_stmt)
            new_users_today = new_users_result.scalar() or 0
            
            # 今日游戏会话数
            today_sessions_stmt = select(func.count(GameSession.id)).where(
                GameSession.started_at >= today_start
            )
            today_sessions_result = await db.execute(today_sessions_stmt)
            today_sessions = today_sessions_result.scalar() or 0
            
            # 🚫 PRD合规性清理：移除金币钻石收入统计 - PRD中没有货币概念
            # PRD中主要统计经验值获得量
            total_experience_stmt = select(func.sum(GameUser.exp))
            total_experience_result = await db.execute(total_experience_stmt)
            total_experience = total_experience_result.scalar() or 0

            # 计算虚拟收入（基于经验值）
            total_revenue = total_experience
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "total_sessions": total_sessions,
                "total_revenue": total_revenue,
                "new_users_today": new_users_today,
                "today_sessions": today_sessions,
                "total_gold": total_gold,
                "total_diamond": total_diamond
            }
            
        except Exception as e:
            logger.error(f"Failed to get overview stats: {e}")
            return {
                "total_users": 0,
                "active_users": 0,
                "total_sessions": 0,
                "total_revenue": 0,
                "new_users_today": 0,
                "today_sessions": 0,
                "total_gold": 0,
                "total_diamond": 0
            }
    
    async def get_user_growth_chart(self, db: AsyncSession, days: int = 7) -> List[Dict]:
        """获取用户增长图表数据"""
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            
            chart_data = []
            
            for i in range(days):
                current_date = start_date + timedelta(days=i)
                next_date = current_date + timedelta(days=1)
                
                # 新增用户
                new_users_stmt = select(func.count(GameUser.id)).where(
                    and_(
                        GameUser.created_at >= datetime.combine(current_date, datetime.min.time()),
                        GameUser.created_at < datetime.combine(next_date, datetime.min.time()),
                        GameUser.is_deleted == False
                    )
                )
                new_users_result = await db.execute(new_users_stmt)
                new_users = new_users_result.scalar() or 0
                
                # 活跃用户（当日有登录记录）
                active_users_stmt = select(func.count(func.distinct(UserLoginLog.user_id))).where(
                    and_(
                        UserLoginLog.login_time >= datetime.combine(current_date, datetime.min.time()),
                        UserLoginLog.login_time < datetime.combine(next_date, datetime.min.time())
                    )
                )
                active_users_result = await db.execute(active_users_stmt)
                active_users = active_users_result.scalar() or 0
                
                chart_data.append({
                    "date": current_date.strftime("%Y-%m-%d"),
                    "newUsers": new_users,
                    "activeUsers": active_users
                })
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Failed to get user growth chart: {e}")
            return []
    
    async def get_revenue_chart(self, db: AsyncSession, days: int = 7) -> List[Dict]:
        """获取收入图表数据"""
        try:
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            
            chart_data = []
            
            for i in range(days):
                current_date = start_date + timedelta(days=i)
                next_date = current_date + timedelta(days=1)
                
                # 🚫 PRD合规性清理：移除金币钻石统计 - PRD中没有货币概念
                # PRD中主要统计经验值获得量
                daily_experience = 0  # 暂时设为0，待实现经验值统计
                diamond_result = await db.execute(diamond_stmt)
                daily_diamond = diamond_result.scalar() or 0
                
                # 计算虚拟收入
                daily_revenue = daily_gold + (daily_diamond * 10)
                
                chart_data.append({
                    "date": current_date.strftime("%Y-%m-%d"),
                    "revenue": daily_revenue,
                    "gold": daily_gold,
                    "diamond": daily_diamond
                })
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Failed to get revenue chart: {e}")
            return []
    
    async def get_game_activity_stats(self, db: AsyncSession) -> Dict:
        """获取游戏活动统计"""
        try:
            # 今日统计
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            
            # 今日完成的会话数
            completed_sessions_stmt = select(func.count(GameSession.id)).where(
                and_(
                    GameSession.started_at >= today_start,
                    GameSession.status == SessionStatus.COMPLETED
                )
            )
            completed_sessions_result = await db.execute(completed_sessions_stmt)
            completed_sessions = completed_sessions_result.scalar() or 0
            
            # 今日活跃会话数
            active_sessions_stmt = select(func.count(GameSession.id)).where(
                and_(
                    GameSession.started_at >= today_start,
                    GameSession.status == SessionStatus.ACTIVE
                )
            )
            active_sessions_result = await db.execute(active_sessions_stmt)
            active_sessions = active_sessions_result.scalar() or 0
            
            # 平均游戏时长
            avg_duration_stmt = select(func.avg(GameSession.duration)).where(
                and_(
                    GameSession.started_at >= today_start,
                    GameSession.status == SessionStatus.COMPLETED,
                    GameSession.duration > 0
                )
            )
            avg_duration_result = await db.execute(avg_duration_stmt)
            avg_duration = avg_duration_result.scalar() or 0
            
            # 今日BOSS战斗次数
            boss_battles_stmt = select(func.count(BossBattle.id)).where(
                BossBattle.battle_time >= today_start
            )
            boss_battles_result = await db.execute(boss_battles_stmt)
            boss_battles = boss_battles_result.scalar() or 0
            
            return {
                "completed_sessions": completed_sessions,
                "active_sessions": active_sessions,
                "avg_duration": round(avg_duration, 2) if avg_duration else 0,
                "boss_battles": boss_battles
            }
            
        except Exception as e:
            logger.error(f"Failed to get game activity stats: {e}")
            return {
                "completed_sessions": 0,
                "active_sessions": 0,
                "avg_duration": 0,
                "boss_battles": 0
            }
    
    async def get_top_players(self, db: AsyncSession, limit: int = 10) -> List[Dict]:
        """获取顶级玩家列表"""
        try:
            # 按等级和经验值排序
            top_players_stmt = select(GameUser).where(
                GameUser.is_deleted == False
            ).order_by(
                desc(GameUser.level),
                desc(GameUser.exp)
            ).limit(limit)
            
            result = await db.execute(top_players_stmt)
            top_players = result.scalars().all()
            
            return [
                {
                    "user_id": player.user_id,
                    "nickname": player.nickname or f"Player_{player.user_id[:8]}",
                    "level": player.level,
                    "exp": player.exp,
                    "total_play_time": player.total_play_time,
                    "vip_level": player.vip_level
                }
                for player in top_players
            ]
            
        except Exception as e:
            logger.error(f"Failed to get top players: {e}")
            return []


# 全局服务实例
dashboard_service = DashboardService()
