<template>
  <div class="hotspot-config">
    <div class="page-header">
      <h2>热点配置管理</h2>
      <p>管理游戏场景中的热点位置和奖励配置</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-row :gutter="16" align="middle">
        <el-col :span="12">
          <el-select
            v-model="selectedCity"
            placeholder="选择城市"
            @change="onCityChange"
            style="margin-right: 16px; width: 150px;"
          >
            <el-option
              v-for="city in cities"
              :key="city.id"
              :label="city.name"
              :value="city.id"
            />
          </el-select>
          
          <el-select
            v-model="selectedScene"
            placeholder="选择场景"
            @change="onSceneChange"
            style="width: 200px;"
          >
            <el-option
              v-for="scene in scenes"
              :key="scene.id"
              :label="scene.name"
              :value="scene.id"
            />
          </el-select>
        </el-col>
        
        <el-col :span="12" style="text-align: right;">
          <el-button
            type="primary"
            icon="Refresh"
            @click="syncXmlToDatabase"
            :loading="syncing"
          >
            同步XML配置
          </el-button>
          
          <el-button
            type="success"
            icon="Upload"
            @click="showImportDialog = true"
          >
            导入热点
          </el-button>
          
          <el-button
            type="warning"
            icon="Download"
            @click="exportHotspots"
          >
            导出热点
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ hotspotStats.total }}</div>
            <div class="stat-label">总热点数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ hotspotStats.thieves }}</div>
            <div class="stat-label">小偷</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ hotspotStats.garbages }}</div>
            <div class="stat-label">垃圾</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card>
          <div class="stat-item">
            <div class="stat-value">{{ hotspotStats.treasures }}</div>
            <div class="stat-label">宝箱</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 热点列表和编辑器 -->
    <el-row :gutter="16" class="main-content">
      <!-- 热点列表 -->
      <el-col :span="12">
        <el-card title="热点列表">
          <template #header>
            <div class="card-header">
              <span>热点列表</span>
              <el-button
                type="primary"
                size="small"
                @click="showCreateDialog = true"
              >
                添加热点
              </el-button>
            </div>
          </template>
          
          <div class="hotspot-list">
            <div
              v-for="hotspot in hotspots"
              :key="hotspot.id"
              class="hotspot-item"
              :class="{ active: selectedHotspot?.id === hotspot.id }"
              @click="selectHotspot(hotspot)"
            >
              <div class="hotspot-info">
                <div class="hotspot-name">
                  <el-tag :type="getHotspotTypeColor(hotspot.type)">
                    {{ getHotspotTypeName(hotspot.type) }}
                  </el-tag>
                  <span>{{ hotspot.name }}</span>
                </div>
                <div class="hotspot-position">
                  位置: ({{ hotspot.position.x.toFixed(1) }}, {{ hotspot.position.y.toFixed(1) }})
                </div>
                <div class="hotspot-reward">
                  奖励: {{ getRewardDescription(hotspot.reward) }}
                </div>
              </div>
              
              <div class="hotspot-actions">
                <el-button
                  type="text"
                  size="small"
                  @click.stop="editHotspot(hotspot)"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  class="danger"
                  @click.stop="deleteHotspot(hotspot)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 热点编辑器 -->
      <el-col :span="12">
        <el-card title="热点编辑器">
          <template #header>
            <div class="card-header">
              <span>热点编辑器</span>
              <el-button
                v-if="selectedHotspot"
                type="success"
                size="small"
                @click="saveHotspot"
                :loading="saving"
              >
                保存修改
              </el-button>
            </div>
          </template>
          
          <div v-if="selectedHotspot" class="hotspot-editor">
            <el-form :model="editForm" label-width="100px">
              <el-form-item label="热点名称">
                <el-input v-model="editForm.name" />
              </el-form-item>
              
              <el-form-item label="热点类型">
                <el-select v-model="editForm.type">
                  <el-option label="小偷" value="thief" />
                  <el-option label="垃圾" value="garbage" />
                  <el-option label="宝箱" value="treasure" />
                  <el-option label="BOSS小偷" value="boss_thief" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="位置坐标">
                <el-row :gutter="8">
                  <el-col :span="12">
                    <el-input-number
                      v-model="editForm.position.x"
                      placeholder="X坐标"
                      :precision="2"
                      :step="0.1"
                      style="width: 100%;"
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input-number
                      v-model="editForm.position.y"
                      placeholder="Y坐标"
                      :precision="2"
                      :step="0.1"
                      style="width: 100%;"
                    />
                  </el-col>
                </el-row>
              </el-form-item>
              
              <el-form-item label="奖励类型">
                <el-select v-model="editForm.reward.type">
                  <el-option label="金币" value="gold" />
                  <el-option label="钻石" value="diamond" />
                  <el-option label="宝箱" value="treasure_box" />
                </el-select>
              </el-form-item>
              
              <el-form-item v-if="editForm.reward.type !== 'treasure_box'" label="奖励数量">
                <el-input-number
                  v-model="editForm.reward.amount"
                  :min="1"
                  :max="1000"
                  style="width: 100%;"
                />
              </el-form-item>
              
              <el-form-item v-if="editForm.reward.type === 'treasure_box'" label="宝箱内容">
                <div class="treasure-box-config">
                  <el-row :gutter="8">
                    <el-col :span="12">
                      <label>金币范围:</label>
                      <el-input-number
                        v-model="editForm.reward.data.contents.gold.min"
                        placeholder="最小值"
                        size="small"
                        style="width: 100%; margin-bottom: 8px;"
                      />
                      <el-input-number
                        v-model="editForm.reward.data.contents.gold.max"
                        placeholder="最大值"
                        size="small"
                        style="width: 100%;"
                      />
                    </el-col>
                    <el-col :span="12">
                      <label>钻石范围:</label>
                      <el-input-number
                        v-model="editForm.reward.data.contents.diamond.min"
                        placeholder="最小值"
                        size="small"
                        style="width: 100%; margin-bottom: 8px;"
                      />
                      <el-input-number
                        v-model="editForm.reward.data.contents.diamond.max"
                        placeholder="最大值"
                        size="small"
                        style="width: 100%;"
                      />
                    </el-col>
                  </el-row>
                </div>
              </el-form-item>
            </el-form>
          </div>
          
          <div v-else class="no-selection">
            <el-empty description="请选择一个热点进行编辑" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="导入热点配置"
      width="600px"
    >
      <el-form :model="importForm" label-width="120px">
        <el-form-item label="导入模式">
          <el-radio-group v-model="importForm.mode">
            <el-radio label="preset">预设配置</el-radio>
            <el-radio label="mixed">混合模式</el-radio>
            <el-radio label="random">随机生成</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="配置文件">
          <el-upload
            :auto-upload="false"
            :on-change="handleFileSelect"
            accept=".json"
            :limit="1"
          >
            <el-button type="primary">选择JSON文件</el-button>
          </el-upload>
        </el-form-item>
        
        <el-form-item v-if="importForm.mode === 'mixed'" label="预设数量">
          <el-input-number v-model="importForm.presetCount" :min="0" :max="20" />
        </el-form-item>
        
        <el-form-item v-if="importForm.mode === 'mixed'" label="随机数量">
          <el-input-number v-model="importForm.randomCount" :min="0" :max="20" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="importHotspots"
          :loading="importing"
        >
          导入
        </el-button>
      </template>
    </el-dialog>

    <!-- 创建热点对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新热点"
      width="500px"
    >
      <el-form :model="createForm" label-width="100px">
        <el-form-item label="热点名称" required>
          <el-input v-model="createForm.name" placeholder="例如: thief_6" />
        </el-form-item>
        
        <el-form-item label="热点类型" required>
          <el-select v-model="createForm.type" style="width: 100%;">
            <el-option label="小偷" value="thief" />
            <el-option label="垃圾" value="garbage" />
            <el-option label="宝箱" value="treasure" />
            <el-option label="BOSS小偷" value="boss_thief" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="位置坐标" required>
          <el-row :gutter="8">
            <el-col :span="12">
              <el-input-number
                v-model="createForm.position.x"
                placeholder="X坐标"
                :precision="2"
                style="width: 100%;"
              />
            </el-col>
            <el-col :span="12">
              <el-input-number
                v-model="createForm.position.y"
                placeholder="Y坐标"
                :precision="2"
                style="width: 100%;"
              />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="createHotspot"
          :loading="creating"
        >
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { gameAPI } from '@/services/api'

// 数据状态
const selectedCity = ref('beijing')
const selectedScene = ref('scene_level_1')
const hotspots = ref([])
const selectedHotspot = ref(null)
const syncing = ref(false)
const saving = ref(false)
const importing = ref(false)
const creating = ref(false)

// 对话框状态
const showImportDialog = ref(false)
const showCreateDialog = ref(false)

// 城市和场景选项
const cities = ref([
  { id: 'beijing', name: '北京' },
  { id: 'shanghai', name: '上海' },
  { id: 'newyork', name: '纽约' },
  { id: 'london', name: '伦敦' }
])

const scenes = ref([
  { id: 'scene_level_1', name: '第一关' },
  { id: 'scene_level_2', name: '第二关' },
  { id: 'scene_level_3', name: '第三关' }
])

// 编辑表单
const editForm = reactive({
  name: '',
  type: 'thief',
  position: { x: 0, y: 0 },
  reward: {
    type: 'gold',
    amount: 10,
    data: {
      contents: {
        gold: { min: 10, max: 30 },
        diamond: { min: 1, max: 3 }
      }
    }
  }
})

// 导入表单
const importForm = reactive({
  mode: 'preset',
  file: null,
  presetCount: 5,
  randomCount: 3
})

// 创建表单
const createForm = reactive({
  name: '',
  type: 'thief',
  position: { x: 0, y: 0 }
})

// 计算属性
const hotspotStats = computed(() => {
  const stats = {
    total: hotspots.value.length,
    thieves: 0,
    garbages: 0,
    treasures: 0
  }
  
  hotspots.value.forEach(hotspot => {
    switch (hotspot.type) {
      case 'thief':
      case 'boss_thief':
        stats.thieves++
        break
      case 'garbage':
        stats.garbages++
        break
      case 'treasure':
        stats.treasures++
        break
    }
  })
  
  return stats
})

// 方法
const loadHotspots = async () => {
  try {
    const response = await gameAPI.getDatabaseHotspots(selectedCity.value, selectedScene.value, true)
    if (response.data.success) {
      hotspots.value = response.data.hotspots || []
    }
  } catch (error) {
    console.error('加载热点失败:', error)
    ElMessage.error('加载热点失败')
  }
}

const onCityChange = () => {
  selectedScene.value = 'scene_level_1'
  loadHotspots()
}

const onSceneChange = () => {
  loadHotspots()
}

const selectHotspot = (hotspot) => {
  selectedHotspot.value = hotspot
  
  // 填充编辑表单
  editForm.name = hotspot.name
  editForm.type = hotspot.type
  editForm.position.x = hotspot.position.x
  editForm.position.y = hotspot.position.y
  editForm.reward = JSON.parse(JSON.stringify(hotspot.reward))
  
  // 确保宝箱数据结构完整
  if (editForm.reward.type === 'treasure_box' && !editForm.reward.data.contents) {
    editForm.reward.data = {
      contents: {
        gold: { min: 10, max: 30 },
        diamond: { min: 1, max: 3 }
      }
    }
  }
}

const saveHotspot = async () => {
  if (!selectedHotspot.value) return
  
  try {
    saving.value = true
    
    const updateData = {
      position: editForm.position,
      type: editForm.type,
      reward: editForm.reward
    }
    
    await gameAPI.updateHotspotConfig(
      selectedCity.value,
      selectedScene.value,
      selectedHotspot.value.name,
      updateData
    )
    
    ElMessage.success('热点更新成功')
    await loadHotspots()
    
  } catch (error) {
    console.error('保存热点失败:', error)
    ElMessage.error('保存热点失败')
  } finally {
    saving.value = false
  }
}

const editHotspot = (hotspot) => {
  selectHotspot(hotspot)
}

const deleteHotspot = async (hotspot) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除热点 "${hotspot.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里需要实现删除API
    ElMessage.success('删除功能开发中...')
    
  } catch (error) {
    // 用户取消删除
  }
}

const syncXmlToDatabase = async () => {
  try {
    syncing.value = true
    
    // 调用同步API
    const response = await gameAPI.syncXmlHotspots({
      scene_file_path: `frontend/public/scenes/${selectedScene.value}.xml`,
      force_update: true
    })
    
    if (response.data.success) {
      ElMessage.success(`同步成功：${response.data.message}`)
      await loadHotspots()
    } else {
      ElMessage.error('同步失败')
    }
    
  } catch (error) {
    console.error('同步XML失败:', error)
    ElMessage.error('同步XML失败')
  } finally {
    syncing.value = false
  }
}

const exportHotspots = async () => {
  try {
    const response = await gameAPI.exportHotspots(selectedCity.value, selectedScene.value)
    
    // 下载JSON文件
    const blob = new Blob([JSON.stringify(response.data.template, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${selectedCity.value}_${selectedScene.value}_hotspots.json`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('导出成功')
    
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

const handleFileSelect = (file) => {
  importForm.file = file.raw
}

const importHotspots = async () => {
  if (!importForm.file) {
    ElMessage.warning('请选择导入文件')
    return
  }
  
  try {
    importing.value = true
    
    // 读取文件内容
    const fileContent = await readFileAsText(importForm.file)
    const jsonData = JSON.parse(fileContent)
    
    // 构建导入请求
    const importData = {
      city_id: selectedCity.value,
      scene_id: selectedScene.value,
      generation_mode: importForm.mode,
      hotspots: jsonData.hotspots || [],
      preset_count: importForm.presetCount,
      random_count: importForm.randomCount
    }
    
    const response = await gameAPI.importHotspots(importData)
    
    if (response.data.success) {
      ElMessage.success('导入成功')
      showImportDialog.value = false
      await loadHotspots()
    } else {
      ElMessage.error('导入失败')
    }
    
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败：文件格式错误')
  } finally {
    importing.value = false
  }
}

const createHotspot = async () => {
  if (!createForm.name || !createForm.type) {
    ElMessage.warning('请填写完整的热点信息')
    return
  }
  
  try {
    creating.value = true
    
    // 生成默认奖励
    const defaultReward = getDefaultReward(createForm.type)
    
    const hotspotData = {
      name: createForm.name,
      type: createForm.type,
      position: createForm.position,
      reward: defaultReward
    }
    
    // 这里需要实现创建API
    ElMessage.success('创建功能开发中...')
    showCreateDialog.value = false
    
  } catch (error) {
    console.error('创建热点失败:', error)
    ElMessage.error('创建热点失败')
  } finally {
    creating.value = false
  }
}

// 工具函数
const getHotspotTypeColor = (type) => {
  const colors = {
    thief: 'danger',
    garbage: 'warning',
    treasure: 'success',
    boss_thief: 'primary'
  }
  return colors[type] || 'info'
}

const getHotspotTypeName = (type) => {
  const names = {
    thief: '小偷',
    garbage: '垃圾',
    treasure: '宝箱',
    boss_thief: 'BOSS'
  }
  return names[type] || type
}

const getRewardDescription = (reward) => {
  if (reward.type === 'treasure_box') {
    const contents = reward.data?.contents || {}
    const goldRange = contents.gold ? `${contents.gold.min}-${contents.gold.max}金币` : ''
    const diamondRange = contents.diamond ? `${contents.diamond.min}-${contents.diamond.max}钻石` : ''
    return `宝箱(${goldRange} ${diamondRange})`
  }
  return `${reward.amount || 0} ${reward.type === 'gold' ? '金币' : '钻石'}`
}

const getDefaultReward = (type) => {
  const defaults = {
    thief: {
      type: 'experience',
      amount: 15,
      data: {}
    },
    garbage: {
      type: 'experience',
      amount: 10,
      data: {}
    },
    treasure: {
      type: 'experience',
      amount: 25,
      data: {}
    },
    boss_thief: {
      type: 'experience',
      amount: 50,
      data: {}
    }
  }
  return defaults[type] || defaults.thief
}

const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = (e) => reject(e)
    reader.readAsText(file)
  })
}

// 生命周期
onMounted(() => {
  loadHotspots()
})
</script>

<style scoped>
.hotspot-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
}

.action-bar {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.main-content {
  min-height: 600px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.hotspot-list {
  max-height: 500px;
  overflow-y: auto;
}

.hotspot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.hotspot-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.12);
}

.hotspot-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.hotspot-info {
  flex: 1;
}

.hotspot-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-weight: 500;
}

.hotspot-position,
.hotspot-reward {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.hotspot-actions {
  display: flex;
  gap: 8px;
}

.hotspot-editor {
  max-height: 500px;
  overflow-y: auto;
}

.no-selection {
  text-align: center;
  padding: 60px 0;
}

.treasure-box-config {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.treasure-box-config label {
  display: block;
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.danger {
  color: #f56c6c !important;
}
</style> 