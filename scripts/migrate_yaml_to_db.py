#!/usr/bin/env python3
"""
YAML热点配置迁移到数据库脚本

用于将existing unified_hotspot_config.yaml中的配置迁移到数据库中的scene_hotspots表
"""

import asyncio
import sys
import os
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

# 设置异步环境
os.environ.setdefault("DATABASE_URL", "mysql+aiomysql://username:password@localhost/universe_vr_game")

from management.backend.app.core.database import get_admin_db
from management.backend.app.models.game_data import SceneHotspot
from management.backend.app.services.hotspot_service_enhanced import hotspot_service_enhanced

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class YAMLHotspotMigrator:
    """YAML热点配置迁移器"""
    
    def __init__(self, yaml_file_path: str, created_by: str = "yaml_migration"):
        self.yaml_file_path = yaml_file_path
        self.created_by = created_by
        self.hotspot_data = []
        
    async def load_yaml_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(self.yaml_file_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                logger.info(f"成功加载YAML配置文件: {self.yaml_file_path}")
                return config
        except FileNotFoundError:
            logger.error(f"配置文件不存在: {self.yaml_file_path}")
            raise
        except yaml.YAMLError as e:
            logger.error(f"YAML解析错误: {e}")
            raise
    
    def extract_hotspots_from_config(self, config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从YAML配置中提取热点数据"""
        hotspots = []
        
        # 获取全局默认配置
        global_defaults = config.get('global_defaults', {})
        global_rewards = global_defaults.get('rewards', {})
        
        # 处理精确热点配置 (precise_hotspots)
        precise_hotspots = config.get('precise_hotspots', {})
        for scene_id, scene_hotspots in precise_hotspots.items():
            city_id = self._extract_city_from_scene(scene_id)
            
            for hotspot_name, hotspot_config in scene_hotspots.items():
                hotspot = self._create_hotspot_from_config(
                    scene_id, city_id, hotspot_name, hotspot_config, global_rewards
                )
                if hotspot:
                    hotspots.append(hotspot)
        
        # 处理场景默认配置 (scene_defaults)
        scene_defaults = config.get('scene_defaults', {})
        for scene_pattern, scene_config in scene_defaults.items():
            # 这里需要根据实际场景名称模式来处理
            # 暂时跳过，因为需要知道具体的场景列表
            logger.info(f"跳过场景默认配置: {scene_pattern}")
        
        # 处理热点类型配置 (hotspot_types) 
        # 这部分通常需要与实际的热点实例结合才能生成具体的热点数据
        hotspot_types = config.get('hotspot_types', {})
        logger.info(f"发现热点类型配置: {list(hotspot_types.keys())}")
        
        return hotspots
    
    def _extract_city_from_scene(self, scene_id: str) -> str:
        """从场景ID提取城市ID"""
        # 根据命名规则提取城市ID
        # 例如: "beijing_scene_01" -> "beijing"
        parts = scene_id.split('_')
        if len(parts) > 0:
            return parts[0]
        return "unknown"
    
    def _create_hotspot_from_config(
        self, 
        scene_id: str, 
        city_id: str, 
        hotspot_name: str, 
        config: Dict[str, Any], 
        global_rewards: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """从配置创建热点数据"""
        try:
            # 基础信息
            hotspot = {
                'scene_id': scene_id,
                'city_id': city_id,
                'hotspot_name': hotspot_name,
                'created_by': self.created_by
            }
            
            # 位置信息
            position = config.get('position', {})
            hotspot['position_x'] = float(position.get('x', 0))
            hotspot['position_y'] = float(position.get('y', 0))
            hotspot['scale'] = float(config.get('scale', 1.0))
            
            # 显示信息
            hotspot['image_url'] = config.get('image_url', '')
            hotspot['visible'] = config.get('visible', True)
            hotspot['enabled'] = config.get('enabled', True)
            hotspot['onclick_action'] = config.get('onclick', '')
            hotspot['description'] = config.get('description', '')
            
            # 热点类型检测
            hotspot['hotspot_type'] = self._detect_hotspot_type(hotspot_name, hotspot['image_url'])
            
            # 奖励信息
            reward_config = config.get('rewards', global_rewards)
            hotspot.update(self._extract_reward_config(reward_config, hotspot['hotspot_type']))
            
            return hotspot
            
        except Exception as e:
            logger.warning(f"创建热点数据失败 {hotspot_name}: {e}")
            return None
    
    def _detect_hotspot_type(self, name: str, url: str) -> str:
        """检测热点类型"""
        name_lower = name.lower()
        url_lower = url.lower()
        
        patterns = {
            'boss_thief': ['boss', 'king', '王'],
            'thief': ['thief', 'steal', '小偷', '盗贼'],
            'garbage': ['garbage', 'trash', 'laji', '垃圾', '军团'],
            'treasure': ['treasure', 'gold', 'box', '宝箱', '宝藏', '金币']
        }
        
        for hotspot_type, keywords in patterns.items():
            for keyword in keywords:
                if keyword in name_lower or keyword in url_lower:
                    return hotspot_type
        
        return 'thief'  # 默认类型
    
    def _extract_reward_config(self, reward_config: Dict[str, Any], hotspot_type: str) -> Dict[str, Any]:
        """提取奖励配置"""
        reward_info = {
            'has_reward': True,
            'reward_type': 'experience',
            'reward_min': 10,
            'reward_max': 10,
            'reward_amount': 10
        }
        
        if not reward_config:
            return reward_info
        
        # 根据热点类型设置默认奖励类型
        type_reward_map = {
            'thief': 'experience',
            'garbage': 'experience',
            'treasure': 'experience',
            'boss_thief': 'experience'
        }
        reward_info['reward_type'] = type_reward_map.get(hotspot_type, 'gold')
        
        # 提取奖励数量
        if 'amount' in reward_config:
            amount = reward_config['amount']
            if isinstance(amount, dict):
                reward_info['reward_min'] = amount.get('min', 10)
                reward_info['reward_max'] = amount.get('max', 10)
            else:
                reward_info['reward_min'] = amount
                reward_info['reward_max'] = amount
                reward_info['reward_amount'] = amount
        
        # 提取奖励类型
        if 'type' in reward_config:
            reward_info['reward_type'] = reward_config['type']
        
        # 检查是否禁用奖励
        if reward_config.get('enabled', True) is False:
            reward_info['has_reward'] = False
        
        return reward_info
    
    async def migrate_to_database(self, dry_run: bool = True) -> Dict[str, Any]:
        """迁移数据到数据库"""
        logger.info("开始迁移热点配置到数据库...")
        
        # 加载YAML配置
        config = await self.load_yaml_config()
        
        # 提取热点数据
        self.hotspot_data = self.extract_hotspots_from_config(config)
        logger.info(f"从YAML配置中提取到 {len(self.hotspot_data)} 个热点")
        
        if dry_run:
            logger.info("DRY RUN模式 - 不会实际写入数据库")
            self._print_migration_summary()
            return {
                "success": True,
                "dry_run": True,
                "total_hotspots": len(self.hotspot_data),
                "hotspots": self.hotspot_data
            }
        
        # 实际迁移到数据库
        async with get_admin_db() as db:
            saved_count = 0
            error_count = 0
            
            for hotspot_data in self.hotspot_data:
                try:
                    # 检查是否已存在
                    existing_query = await db.execute(
                        "SELECT id FROM scene_hotspots WHERE scene_id = %s AND city_id = %s AND hotspot_name = %s",
                        (hotspot_data['scene_id'], hotspot_data['city_id'], hotspot_data['hotspot_name'])
                    )
                    existing = existing_query.fetchone()
                    
                    if existing:
                        logger.info(f"热点已存在，跳过: {hotspot_data['hotspot_name']}")
                        continue
                    
                    # 创建新热点
                    hotspot = SceneHotspot(**hotspot_data)
                    db.add(hotspot)
                    saved_count += 1
                    
                except Exception as e:
                    logger.error(f"保存热点失败 {hotspot_data.get('hotspot_name', 'unknown')}: {e}")
                    error_count += 1
            
            try:
                await db.commit()
                logger.info(f"迁移完成: 成功保存 {saved_count} 个热点，{error_count} 个错误")
            except Exception as e:
                await db.rollback()
                logger.error(f"数据库提交失败: {e}")
                raise
        
        return {
            "success": True,
            "dry_run": False,
            "total_hotspots": len(self.hotspot_data),
            "saved_count": saved_count,
            "error_count": error_count
        }
    
    def _print_migration_summary(self):
        """打印迁移摘要"""
        logger.info("\n=== 迁移摘要 ===")
        logger.info(f"总热点数: {len(self.hotspot_data)}")
        
        # 按场景统计
        scene_stats = {}
        type_stats = {}
        reward_stats = {}
        
        for hotspot in self.hotspot_data:
            scene_id = hotspot['scene_id']
            hotspot_type = hotspot['hotspot_type']
            reward_type = hotspot['reward_type']
            
            scene_stats[scene_id] = scene_stats.get(scene_id, 0) + 1
            type_stats[hotspot_type] = type_stats.get(hotspot_type, 0) + 1
            reward_stats[reward_type] = reward_stats.get(reward_type, 0) + 1
        
        logger.info("\n按场景统计:")
        for scene_id, count in scene_stats.items():
            logger.info(f"  {scene_id}: {count} 个热点")
        
        logger.info("\n按类型统计:")
        for hotspot_type, count in type_stats.items():
            logger.info(f"  {hotspot_type}: {count} 个热点")
        
        logger.info("\n按奖励类型统计:")
        for reward_type, count in reward_stats.items():
            logger.info(f"  {reward_type}: {count} 个热点")
        
        logger.info("\n前5个热点示例:")
        for i, hotspot in enumerate(self.hotspot_data[:5]):
            logger.info(f"  {i+1}. {hotspot['scene_id']}/{hotspot['hotspot_name']} ({hotspot['hotspot_type']})")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='迁移YAML热点配置到数据库')
    parser.add_argument('yaml_file', help='YAML配置文件路径')
    parser.add_argument('--dry-run', action='store_true', help='只显示迁移计划，不实际执行')
    parser.add_argument('--created-by', default='yaml_migration', help='创建者标识')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.yaml_file):
        logger.error(f"配置文件不存在: {args.yaml_file}")
        sys.exit(1)
    
    migrator = YAMLHotspotMigrator(args.yaml_file, args.created_by)
    
    try:
        result = await migrator.migrate_to_database(dry_run=args.dry_run)
        
        if result['success']:
            if result['dry_run']:
                logger.info("\nDRY RUN完成，使用 --no-dry-run 参数执行实际迁移")
            else:
                logger.info(f"\n迁移完成: 成功保存 {result['saved_count']} 个热点")
        else:
            logger.error("迁移失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"迁移过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())